# API接口设计文档

## 概述

集群任务管理平台使用HTTP REST API进行组件间通信。所有API响应使用JSON格式。

## 基础信息

- **Base URL**: `http://{manager_host}:{manager_port}/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: 暂不实现，后续可添加Token认证

## 通用响应格式

```json
{
    "success": true,
    "message": "操作成功",
    "data": {},
    "timestamp": 1640995200.0
}
```

## Runner管理API

### 1. Runner注册
**POST** `/runners/register`

注册新的Runner到管理程序。

**请求体**:
```json
{
    "runner_id": "runner-001",
    "name": "Worker Node 1",
    "host": "*************",
    "port": 8081,
    "resource_limits": {
        "max_cpu_percent": 80.0,
        "max_memory_mb": 2048,
        "max_runtime_seconds": 7200
    },
    "capabilities": ["python", "data-processing"]
}
```

**响应**:
```json
{
    "success": true,
    "message": "Runner注册成功",
    "data": {
        "runner_id": "runner-001",
        "status": "idle"
    }
}
```

### 2. Runner注销
**POST** `/runners/{runner_id}/unregister`

注销Runner。

### 3. 心跳检测
**POST** `/runners/{runner_id}/heartbeat`

Runner定期发送心跳信息。

**请求体**:
```json
{
    "status": "idle",
    "resource_usage": {
        "cpu_percent": 25.5,
        "memory_mb": 512,
        "runtime_seconds": 1800
    },
    "current_job_id": null
}
```

### 4. 获取Runner列表
**GET** `/runners`

获取所有注册的Runner信息。

## 任务管理API

### 1. 提交任务
**POST** `/jobs`

提交新任务到系统。

**请求体**:
```json
{
    "name": "数据处理任务",
    "description": "处理用户数据文件",
    "command": "python",
    "args": ["process_data.py", "--input", "data.csv"],
    "env": {
        "PYTHONPATH": "/app"
    },
    "working_dir": "/app/workspace",
    "priority": 5,
    "max_retries": 3,
    "dependencies": []
}
```

### 2. 获取下一个任务
**GET** `/jobs/next`

Runner请求获取下一个待执行的任务。

**查询参数**:
- `runner_id`: Runner ID
- `capabilities`: Runner能力标签（可选）

**响应**:
```json
{
    "success": true,
    "data": {
        "job_id": "job-001",
        "name": "数据处理任务",
        "command": "python",
        "args": ["process_data.py"],
        "env": {},
        "working_dir": "/app/workspace"
    }
}
```

### 3. 更新任务状态
**POST** `/jobs/{job_id}/status`

更新任务执行状态。

**请求体**:
```json
{
    "status": "running",
    "runner_id": "runner-001",
    "progress": 50,
    "message": "正在处理数据...",
    "result": null
}
```

### 4. 获取任务详情
**GET** `/jobs/{job_id}`

获取指定任务的详细信息。

### 5. 获取任务列表
**GET** `/jobs`

获取任务列表，支持分页和过滤。

**查询参数**:
- `status`: 任务状态过滤
- `page`: 页码（默认1）
- `size`: 每页大小（默认20）

## 队列管理API

### 1. 创建任务队列
**POST** `/queues`

创建新的任务队列。

### 2. 获取队列列表
**GET** `/queues`

获取所有任务队列。

### 3. 向队列添加任务
**POST** `/queues/{queue_id}/jobs`

向指定队列添加任务。

## 监控API

### 1. 系统状态
**GET** `/status`

获取系统整体状态。

**响应**:
```json
{
    "success": true,
    "data": {
        "total_runners": 5,
        "active_runners": 3,
        "total_jobs": 100,
        "pending_jobs": 10,
        "running_jobs": 5,
        "completed_jobs": 85,
        "system_load": {
            "cpu_usage": 45.2,
            "memory_usage": 60.8
        }
    }
}
```

### 2. 性能指标
**GET** `/metrics`

获取系统性能指标。

## 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 消息格式规范

### 心跳消息
```json
{
    "message_type": "heartbeat",
    "runner_id": "runner-001",
    "timestamp": 1640995200.0,
    "data": {
        "status": "idle",
        "resource_usage": {...}
    }
}
```

### 任务状态更新消息
```json
{
    "message_type": "status_update",
    "job_id": "job-001",
    "runner_id": "runner-001",
    "timestamp": 1640995200.0,
    "data": {
        "status": "completed",
        "result": {...}
    }
}
```
