"""
集群任务管理平台 - 核心管理器
"""

import threading
import time
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor

from common import (
    get_logger, LoggerMixin, ConfigManager, Job, Runner, JobStatus, RunnerStatus,
    generate_id, get_timestamp
)
from .job_manager import JobManager
from .runner_manager import RunnerManager
from .api_server import APIServer
from .scheduler import TaskScheduler
from .monitor import SystemMonitor


class ClusterManager(LoggerMixin):
    """集群管理器主类"""

    def __init__(self, config: ConfigManager):
        self.config = config
        self.running = False
        self.shutdown_event = threading.Event()

        # 获取管理器配置
        manager_config = config.get_section('manager')
        self.host = manager_config.get('host', 'localhost')
        self.port = manager_config.get('port', 8080)
        self.max_workers = manager_config.get('max_workers', 10)
        self.heartbeat_timeout = manager_config.get('heartbeat_timeout', 60)
        self.cleanup_interval = manager_config.get('job_cleanup_interval', 300)

        # 初始化子组件
        self.job_manager = JobManager(config)
        self.runner_manager = RunnerManager(config)
        self.scheduler = TaskScheduler(config, self.job_manager, self.runner_manager)
        self.monitor = SystemMonitor(config)
        self.api_server = APIServer(
            config, self.job_manager, self.runner_manager,
            self.scheduler, self.monitor
        )

        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # 后台任务线程
        self.background_threads = []

        self.logger.info("集群管理器初始化完成")

    def start(self):
        """启动管理器"""
        if self.running:
            self.logger.warning("管理器已经在运行")
            return

        self.logger.info("正在启动集群管理器...")

        try:
            # 启动子组件
            self.job_manager.start()
            self.runner_manager.start()
            self.scheduler.start()
            self.monitor.start()

            # 启动API服务器
            self.api_server.start(self.host, self.port)

            # 启动后台任务
            self._start_background_tasks()

            self.running = True
            self.logger.info("集群管理器启动成功")

        except Exception as e:
            self.logger.error(f"启动集群管理器失败: {e}")
            self.shutdown()
            raise

    def _start_background_tasks(self):
        """启动后台任务"""
        # 心跳检查任务
        heartbeat_thread = threading.Thread(
            target=self._heartbeat_checker,
            name="HeartbeatChecker",
            daemon=True
        )
        heartbeat_thread.start()
        self.background_threads.append(heartbeat_thread)

        # 清理任务
        cleanup_thread = threading.Thread(
            target=self._cleanup_task,
            name="CleanupTask",
            daemon=True
        )
        cleanup_thread.start()
        self.background_threads.append(cleanup_thread)

        # 调度任务
        scheduler_thread = threading.Thread(
            target=self._scheduler_task,
            name="SchedulerTask",
            daemon=True
        )
        scheduler_thread.start()
        self.background_threads.append(scheduler_thread)

        self.logger.info(f"启动了 {len(self.background_threads)} 个后台任务")

    def _heartbeat_checker(self):
        """心跳检查任务"""
        self.logger.info("心跳检查任务启动")

        while not self.shutdown_event.is_set():
            try:
                current_time = get_timestamp()
                timeout_runners = []

                # 检查所有Runner的心跳
                for runner_id, runner in self.runner_manager.get_all_runners().items():
                    if current_time - runner.last_heartbeat > self.heartbeat_timeout:
                        timeout_runners.append(runner_id)

                # 处理超时的Runner
                for runner_id in timeout_runners:
                    self.logger.warning(f"Runner {runner_id} 心跳超时，标记为离线")
                    self.runner_manager.mark_runner_offline(runner_id)

                # 等待下一次检查
                self.shutdown_event.wait(30)  # 每30秒检查一次

            except Exception as e:
                self.logger.error(f"心跳检查任务异常: {e}")
                self.shutdown_event.wait(10)

    def _cleanup_task(self):
        """清理任务"""
        self.logger.info("清理任务启动")

        while not self.shutdown_event.is_set():
            try:
                # 清理完成的任务
                self.job_manager.cleanup_completed_jobs()

                # 清理离线的Runner
                self.runner_manager.cleanup_offline_runners()

                # 等待下一次清理
                self.shutdown_event.wait(self.cleanup_interval)

            except Exception as e:
                self.logger.error(f"清理任务异常: {e}")
                self.shutdown_event.wait(60)

    def _scheduler_task(self):
        """调度任务"""
        self.logger.info("调度任务启动")

        while not self.shutdown_event.is_set():
            try:
                # 执行任务调度
                self.scheduler.schedule_jobs()

                # 等待下一次调度
                self.shutdown_event.wait(5)  # 每5秒调度一次

            except Exception as e:
                self.logger.error(f"调度任务异常: {e}")
                self.shutdown_event.wait(10)

    def run(self):
        """运行管理器（阻塞）"""
        if not self.running:
            raise RuntimeError("管理器未启动")

        self.logger.info("集群管理器开始运行...")

        try:
            # 等待关闭信号
            self.shutdown_event.wait()
        except KeyboardInterrupt:
            self.logger.info("接收到中断信号")
        finally:
            self.shutdown()

    def shutdown(self):
        """关闭管理器"""
        if not self.running:
            return

        self.logger.info("正在关闭集群管理器...")

        # 设置关闭事件
        self.shutdown_event.set()

        # 关闭API服务器
        self.api_server.shutdown()

        # 关闭子组件
        self.scheduler.shutdown()
        self.monitor.shutdown()
        self.runner_manager.shutdown()
        self.job_manager.shutdown()

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 等待后台线程结束
        for thread in self.background_threads:
            if thread.is_alive():
                thread.join(timeout=5)

        self.running = False
        self.logger.info("集群管理器已关闭")

    def get_status(self) -> Dict:
        """获取管理器状态"""
        return {
            "running": self.running,
            "host": self.host,
            "port": self.port,
            "jobs": self.job_manager.get_status(),
            "runners": self.runner_manager.get_status(),
            "scheduler": self.scheduler.get_status(),
            "monitor": self.monitor.get_status(),
            "uptime": time.time() - getattr(self, '_start_time', time.time())
        }

    def submit_job(self, job: Job) -> str:
        """提交任务"""
        return self.job_manager.submit_job(job)

    def get_job(self, job_id: str) -> Optional[Job]:
        """获取任务"""
        return self.job_manager.get_job(job_id)

    def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        return self.job_manager.cancel_job(job_id)

    def register_runner(self, runner: Runner) -> bool:
        """注册Runner"""
        return self.runner_manager.register_runner(runner)

    def unregister_runner(self, runner_id: str) -> bool:
        """注销Runner"""
        return self.runner_manager.unregister_runner(runner_id)

    def get_runner(self, runner_id: str) -> Optional[Runner]:
        """获取Runner"""
        return self.runner_manager.get_runner(runner_id)
