#!/usr/bin/env python3
"""
集群任务管理平台 - 管理程序主入口
"""

import sys
import signal
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from common import get_logger, get_config, ConfigManager
from .manager import ClusterManager


def setup_signal_handlers(manager: ClusterManager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger = get_logger('cluster.manager.main')
        logger.info(f"接收到信号 {signum}，正在关闭管理程序...")
        manager.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='集群任务管理平台 - 管理程序')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/default.json',
        help='配置文件路径 (默认: config/default.json)'
    )
    
    parser.add_argument(
        '--host',
        type=str,
        help='服务器监听地址 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        help='服务器监听端口 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--daemon', '-d',
        action='store_true',
        help='以守护进程模式运行'
    )
    
    parser.add_argument(
        '--pid-file',
        type=str,
        default='cluster_manager.pid',
        help='PID文件路径 (守护进程模式)'
    )
    
    return parser.parse_args()


def setup_daemon(pid_file: str):
    """设置守护进程"""
    import os
    import atexit
    
    # 第一次fork
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)  # 父进程退出
    except OSError as e:
        sys.stderr.write(f"第一次fork失败: {e}\n")
        sys.exit(1)
    
    # 脱离父进程环境
    os.chdir('/')
    os.setsid()
    os.umask(0)
    
    # 第二次fork
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)  # 父进程退出
    except OSError as e:
        sys.stderr.write(f"第二次fork失败: {e}\n")
        sys.exit(1)
    
    # 重定向标准输入输出
    sys.stdout.flush()
    sys.stderr.flush()
    
    with open('/dev/null', 'r') as si:
        os.dup2(si.fileno(), sys.stdin.fileno())
    with open('/dev/null', 'w') as so:
        os.dup2(so.fileno(), sys.stdout.fileno())
    with open('/dev/null', 'w') as se:
        os.dup2(se.fileno(), sys.stderr.fileno())
    
    # 写入PID文件
    pid = str(os.getpid())
    with open(pid_file, 'w') as f:
        f.write(f"{pid}\n")
    
    # 注册退出时删除PID文件
    atexit.register(lambda: os.remove(pid_file) if os.path.exists(pid_file) else None)


def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        # 加载配置
        config = get_config(args.config)
        
        # 命令行参数覆盖配置
        if args.host:
            config.set('manager.host', args.host)
        if args.port:
            config.set('manager.port', args.port)
        if args.log_level:
            config.set('logging.level', args.log_level)
        
        # 验证配置
        config.validate()
        
        # 设置日志
        from common.logger import ClusterLogger
        logging_config = config.get_section('logging')
        logger = ClusterLogger.get_logger('cluster.manager.main', logging_config)
        
        logger.info("=" * 50)
        logger.info("集群任务管理平台 - 管理程序启动")
        logger.info("=" * 50)
        
        # 守护进程模式
        if args.daemon:
            logger.info("以守护进程模式启动...")
            setup_daemon(args.pid_file)
            # 重新获取logger，因为标准输出已重定向
            logger = ClusterLogger.get_logger('cluster.manager.main', logging_config)
        
        # 创建并启动管理程序
        manager = ClusterManager(config)
        
        # 设置信号处理器
        setup_signal_handlers(manager)
        
        # 启动管理程序
        logger.info("正在启动集群管理程序...")
        manager.start()
        
        logger.info("集群管理程序启动成功")
        logger.info(f"服务地址: http://{config.get('manager.host')}:{config.get('manager.port')}")
        
        # 保持运行
        manager.run()
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"管理程序启动失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("集群管理程序已关闭")


if __name__ == '__main__':
    main()
