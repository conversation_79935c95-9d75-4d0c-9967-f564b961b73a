"""
集群任务管理平台 - API服务器
"""

import json
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from typing import Dict, Any, Optional

from common import (
    get_logger, LoggerMixin, ConfigManager, Job, Runner, JobStatus, RunnerStatus,
    MessageSerializer, get_timestamp
)


class APIHandler(BaseHTTPRequestHandler):
    """API请求处理器"""
    
    def __init__(self, job_manager, runner_manager, scheduler, monitor, *args, **kwargs):
        self.job_manager = job_manager
        self.runner_manager = runner_manager
        self.scheduler = scheduler
        self.monitor = monitor
        self.logger = get_logger('cluster.api')
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            if path == '/api/v1/status':
                self._handle_get_status()
            elif path == '/api/v1/jobs':
                self._handle_get_jobs(query_params)
            elif path.startswith('/api/v1/jobs/'):
                job_id = path.split('/')[-1]
                self._handle_get_job(job_id)
            elif path == '/api/v1/jobs/next':
                self._handle_get_next_job(query_params)
            elif path == '/api/v1/runners':
                self._handle_get_runners()
            elif path.startswith('/api/v1/runners/'):
                runner_id = path.split('/')[-1]
                self._handle_get_runner(runner_id)
            elif path == '/api/v1/metrics':
                self._handle_get_metrics(query_params)
            elif path == '/api/v1/alerts':
                self._handle_get_alerts()
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"处理GET请求失败: {e}")
            self._send_error(500, str(e))
    
    def do_POST(self):
        """处理POST请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            request_body = self.rfile.read(content_length).decode('utf-8')
            request_data = json.loads(request_body) if request_body else {}
            
            if path == '/api/v1/jobs':
                self._handle_submit_job(request_data)
            elif path == '/api/v1/runners/register':
                self._handle_register_runner(request_data)
            elif path.startswith('/api/v1/runners/') and path.endswith('/heartbeat'):
                runner_id = path.split('/')[-2]
                self._handle_runner_heartbeat(runner_id, request_data)
            elif path.startswith('/api/v1/runners/') and path.endswith('/unregister'):
                runner_id = path.split('/')[-2]
                self._handle_unregister_runner(runner_id)
            elif path.startswith('/api/v1/jobs/') and path.endswith('/status'):
                job_id = path.split('/')[-2]
                self._handle_update_job_status(job_id, request_data)
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"处理POST请求失败: {e}")
            self._send_error(500, str(e))
    
    def do_DELETE(self):
        """处理DELETE请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path.startswith('/api/v1/jobs/'):
                job_id = path.split('/')[-1]
                self._handle_cancel_job(job_id)
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"处理DELETE请求失败: {e}")
            self._send_error(500, str(e))
    
    def _handle_get_status(self):
        """获取系统状态"""
        status = {
            'timestamp': get_timestamp(),
            'jobs': self.job_manager.get_status(),
            'runners': self.runner_manager.get_status(),
            'scheduler': self.scheduler.get_status(),
            'monitor': self.monitor.get_status()
        }
        self._send_json_response(status)
    
    def _handle_get_jobs(self, query_params):
        """获取任务列表"""
        status_filter = query_params.get('status', [None])[0]
        limit = int(query_params.get('limit', [20])[0])
        
        status = JobStatus(status_filter) if status_filter else None
        jobs = self.job_manager.get_jobs(status, limit)
        
        jobs_data = [MessageSerializer.serialize_job(job) for job in jobs]
        self._send_json_response({'jobs': jobs_data})
    
    def _handle_get_job(self, job_id):
        """获取单个任务"""
        job = self.job_manager.get_job(job_id)
        if job:
            job_data = MessageSerializer.serialize_job(job)
            self._send_json_response(job_data)
        else:
            self._send_error(404, "Job not found")
    
    def _handle_get_next_job(self, query_params):
        """获取下一个任务"""
        runner_id = query_params.get('runner_id', [None])[0]
        capabilities = query_params.get('capabilities', [])
        
        if not runner_id:
            self._send_error(400, "Missing runner_id parameter")
            return
        
        job = self.job_manager.get_next_job(capabilities)
        if job:
            job_data = MessageSerializer.serialize_job(job)
            self._send_json_response(job_data)
        else:
            self._send_json_response({'message': 'No jobs available'})
    
    def _handle_submit_job(self, request_data):
        """提交任务"""
        try:
            job = MessageSerializer.deserialize_job(json.dumps(request_data))
            job_id = self.job_manager.submit_job(job)
            
            # 记录监控指标
            self.monitor.record_job_metrics(job_id, 'submitted')
            
            self._send_json_response({'job_id': job_id, 'message': 'Job submitted successfully'})
            
        except Exception as e:
            self._send_error(400, f"Invalid job data: {e}")
    
    def _handle_get_runners(self):
        """获取Runner列表"""
        runners = self.runner_manager.get_all_runners()
        runners_data = {rid: MessageSerializer.serialize_runner(runner) 
                       for rid, runner in runners.items()}
        self._send_json_response({'runners': runners_data})
    
    def _handle_get_runner(self, runner_id):
        """获取单个Runner"""
        runner = self.runner_manager.get_runner(runner_id)
        if runner:
            runner_data = MessageSerializer.serialize_runner(runner)
            self._send_json_response(runner_data)
        else:
            self._send_error(404, "Runner not found")
    
    def _handle_register_runner(self, request_data):
        """注册Runner"""
        try:
            runner = MessageSerializer.deserialize_runner(json.dumps(request_data))
            success = self.runner_manager.register_runner(runner)
            
            if success:
                self._send_json_response({
                    'runner_id': runner.runner_id,
                    'message': 'Runner registered successfully'
                })
            else:
                self._send_error(409, "Runner registration failed")
                
        except Exception as e:
            self._send_error(400, f"Invalid runner data: {e}")
    
    def _handle_unregister_runner(self, runner_id):
        """注销Runner"""
        success = self.runner_manager.unregister_runner(runner_id)
        if success:
            self._send_json_response({'message': 'Runner unregistered successfully'})
        else:
            self._send_error(404, "Runner not found")
    
    def _handle_runner_heartbeat(self, runner_id, request_data):
        """处理Runner心跳"""
        status = request_data.get('status')
        resource_usage = request_data.get('resource_usage', {})
        current_job_id = request_data.get('current_job_id')
        
        status_enum = RunnerStatus(status) if status else None
        
        success = self.runner_manager.update_heartbeat(
            runner_id, status_enum, resource_usage, current_job_id
        )
        
        if success:
            # 记录监控指标
            self.monitor.record_runner_metrics(runner_id, status, resource_usage)
            self._send_json_response({'message': 'Heartbeat received'})
        else:
            self._send_error(404, "Runner not found")
    
    def _handle_update_job_status(self, job_id, request_data):
        """更新任务状态"""
        status = request_data.get('status')
        runner_id = request_data.get('runner_id')
        result = request_data.get('result')
        error_message = request_data.get('error_message')
        
        if not status:
            self._send_error(400, "Missing status parameter")
            return
        
        status_enum = JobStatus(status)
        success = self.job_manager.update_job_status(
            job_id, status_enum, runner_id, result, error_message
        )
        
        if success:
            # 记录监控指标
            job = self.job_manager.get_job(job_id)
            duration = None
            if job and job.started_at and job.completed_at:
                duration = job.completed_at - job.started_at
            
            self.monitor.record_job_metrics(job_id, status, duration, runner_id)
            
            # 处理任务完成
            if status_enum in [JobStatus.COMPLETED, JobStatus.FAILED]:
                self.scheduler.handle_job_completion(
                    job_id, runner_id, status_enum == JobStatus.COMPLETED, result
                )
            
            self._send_json_response({'message': 'Job status updated'})
        else:
            self._send_error(404, "Job not found")
    
    def _handle_cancel_job(self, job_id):
        """取消任务"""
        success = self.job_manager.cancel_job(job_id)
        if success:
            self.monitor.record_job_metrics(job_id, 'cancelled')
            self._send_json_response({'message': 'Job cancelled successfully'})
        else:
            self._send_error(404, "Job not found or cannot be cancelled")
    
    def _handle_get_metrics(self, query_params):
        """获取监控指标"""
        metric_type = query_params.get('type', ['system'])[0]
        hours = int(query_params.get('hours', [1])[0])
        
        metrics = self.monitor.get_metrics_history(metric_type, hours)
        self._send_json_response({'metrics': metrics})
    
    def _handle_get_alerts(self):
        """获取告警列表"""
        alerts = self.monitor.get_alerts()
        self._send_json_response({'alerts': alerts})
    
    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """发送JSON响应"""
        response = {
            'success': True,
            'data': data,
            'timestamp': get_timestamp()
        }
        
        response_json = json.dumps(response, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response_json.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(response_json.encode('utf-8'))
    
    def _send_error(self, status_code: int, message: str):
        """发送错误响应"""
        response = {
            'success': False,
            'error': message,
            'timestamp': get_timestamp()
        }
        
        response_json = json.dumps(response, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response_json.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(response_json.encode('utf-8'))
    
    def log_message(self, format, *args):
        """重写日志方法，使用我们的日志器"""
        self.logger.info(f"{self.address_string()} - {format % args}")


class APIServer(LoggerMixin):
    """API服务器"""
    
    def __init__(self, config: ConfigManager, job_manager, runner_manager, scheduler, monitor):
        self.config = config
        self.job_manager = job_manager
        self.runner_manager = runner_manager
        self.scheduler = scheduler
        self.monitor = monitor
        
        self.server = None
        self.server_thread = None
        self.running = False
        
        self.logger.info("API服务器初始化完成")
    
    def start(self, host: str, port: int):
        """启动API服务器"""
        if self.running:
            return
        
        self.logger.info(f"启动API服务器: {host}:{port}")
        
        # 创建处理器工厂
        def handler_factory(*args, **kwargs):
            return APIHandler(
                self.job_manager, self.runner_manager, 
                self.scheduler, self.monitor, *args, **kwargs
            )
        
        # 创建HTTP服务器
        self.server = HTTPServer((host, port), handler_factory)
        
        # 启动服务器线程
        self.server_thread = threading.Thread(
            target=self.server.serve_forever,
            name="APIServer",
            daemon=True
        )
        self.server_thread.start()
        
        self.running = True
        self.logger.info(f"API服务器启动成功: http://{host}:{port}")
    
    def shutdown(self):
        """关闭API服务器"""
        if not self.running:
            return
        
        self.logger.info("关闭API服务器...")
        
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5)
        
        self.running = False
        self.logger.info("API服务器已关闭")
