"""
集群任务管理平台 - Runner管理器
"""

import threading
import time
from typing import Dict, List, Optional, Set
from collections import defaultdict

from common import (
    get_logger, LoggerMixin, Config<PERSON>ana<PERSON>, Runner, RunnerStatus,
    generate_id, get_timestamp, DataPersistence
)


class RunnerManager(LoggerMixin):
    """Runner管理器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.running = False
        
        # Runner存储
        self.runners: Dict[str, Runner] = {}
        self.active_runners: Set[str] = set()
        self.idle_runners: Set[str] = set()
        self.busy_runners: Set[str] = set()
        self.offline_runners: Set[str] = set()
        
        # Runner能力索引
        self.capability_index: Dict[str, Set[str]] = defaultdict(set)
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 配置参数
        manager_config = config.get_section('manager')
        self.heartbeat_timeout = manager_config.get('heartbeat_timeout', 60)
        
        # 持久化配置
        self.persistence_enabled = config.get('database.type') is not None
        self.persistence_path = config.get('database.path', 'data/runners.json')
        
        self.logger.info("Runner管理器初始化完成")
    
    def start(self):
        """启动Runner管理器"""
        if self.running:
            return
        
        self.logger.info("启动Runner管理器...")
        
        # 加载持久化的Runner信息
        if self.persistence_enabled:
            self._load_runners()
        
        self.running = True
        self.logger.info("Runner管理器启动成功")
    
    def shutdown(self):
        """关闭Runner管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭Runner管理器...")
        
        # 通知所有Runner关闭
        with self.lock:
            for runner_id in list(self.active_runners):
                self._notify_runner_shutdown(runner_id)
        
        # 保存Runner状态
        if self.persistence_enabled:
            self._save_runners()
        
        self.running = False
        self.logger.info("Runner管理器已关闭")
    
    def register_runner(self, runner: Runner) -> bool:
        """注册Runner"""
        with self.lock:
            # 生成Runner ID（如果没有）
            if not runner.runner_id:
                runner.runner_id = generate_id("runner")
            
            # 检查是否已存在
            if runner.runner_id in self.runners:
                existing_runner = self.runners[runner.runner_id]
                if existing_runner.status != RunnerStatus.STOPPED:
                    self.logger.warning(f"Runner已存在且处于活动状态: {runner.runner_id}")
                    return False
            
            # 设置默认值
            if not runner.registered_at:
                runner.registered_at = get_timestamp()
            runner.last_heartbeat = get_timestamp()
            runner.status = RunnerStatus.IDLE
            
            # 验证Runner
            self._validate_runner(runner)
            
            # 存储Runner
            self.runners[runner.runner_id] = runner
            self.active_runners.add(runner.runner_id)
            self.idle_runners.add(runner.runner_id)
            
            # 更新能力索引
            self._update_capability_index(runner.runner_id, runner.capabilities)
            
            self.logger.info(f"Runner已注册: {runner.runner_id} - {runner.name}")
            
            # 持久化
            if self.persistence_enabled:
                self._save_runner(runner)
            
            return True
    
    def _validate_runner(self, runner: Runner):
        """验证Runner"""
        if not runner.name:
            raise ValueError("Runner名称不能为空")
        if not runner.host:
            raise ValueError("Runner主机地址不能为空")
        if runner.port <= 0 or runner.port > 65535:
            raise ValueError("Runner端口号无效")
    
    def _update_capability_index(self, runner_id: str, capabilities: List[str]):
        """更新能力索引"""
        # 清除旧的索引
        for capability_set in self.capability_index.values():
            capability_set.discard(runner_id)
        
        # 添加新的索引
        for capability in capabilities:
            self.capability_index[capability].add(runner_id)
    
    def unregister_runner(self, runner_id: str) -> bool:
        """注销Runner"""
        with self.lock:
            runner = self.runners.get(runner_id)
            if not runner:
                self.logger.warning(f"尝试注销不存在的Runner: {runner_id}")
                return False
            
            # 更新状态
            runner.status = RunnerStatus.STOPPED
            
            # 从活动集合中移除
            self.active_runners.discard(runner_id)
            self.idle_runners.discard(runner_id)
            self.busy_runners.discard(runner_id)
            self.offline_runners.add(runner_id)
            
            # 清除能力索引
            self._update_capability_index(runner_id, [])
            
            self.logger.info(f"Runner已注销: {runner_id}")
            
            # 持久化
            if self.persistence_enabled:
                self._save_runner(runner)
            
            return True
    
    def update_heartbeat(self, runner_id: str, status: RunnerStatus = None, 
                        resource_usage: dict = None, current_job_id: str = None) -> bool:
        """更新Runner心跳"""
        with self.lock:
            runner = self.runners.get(runner_id)
            if not runner:
                self.logger.warning(f"收到未知Runner的心跳: {runner_id}")
                return False
            
            # 更新心跳时间
            runner.last_heartbeat = get_timestamp()
            
            # 更新状态
            if status and status != runner.status:
                old_status = runner.status
                runner.status = status
                self._update_runner_status_sets(runner_id, old_status, status)
            
            # 更新资源使用情况
            if resource_usage:
                runner.resource_usage.cpu_percent = resource_usage.get('cpu_percent', 0)
                runner.resource_usage.memory_mb = resource_usage.get('memory_mb', 0)
                runner.resource_usage.runtime_seconds = resource_usage.get('runtime_seconds', 0)
                runner.resource_usage.timestamp = get_timestamp()
            
            # 更新当前任务
            if current_job_id is not None:
                runner.current_job_id = current_job_id
            
            return True
    
    def _update_runner_status_sets(self, runner_id: str, old_status: RunnerStatus, new_status: RunnerStatus):
        """更新Runner状态集合"""
        # 从旧状态集合中移除
        if old_status == RunnerStatus.IDLE:
            self.idle_runners.discard(runner_id)
        elif old_status == RunnerStatus.BUSY:
            self.busy_runners.discard(runner_id)
        
        # 添加到新状态集合
        if new_status == RunnerStatus.IDLE:
            self.idle_runners.add(runner_id)
            self.busy_runners.discard(runner_id)
        elif new_status == RunnerStatus.BUSY:
            self.busy_runners.add(runner_id)
            self.idle_runners.discard(runner_id)
        elif new_status == RunnerStatus.STOPPING:
            self.idle_runners.discard(runner_id)
            self.busy_runners.discard(runner_id)
        elif new_status == RunnerStatus.STOPPED:
            self.active_runners.discard(runner_id)
            self.idle_runners.discard(runner_id)
            self.busy_runners.discard(runner_id)
            self.offline_runners.add(runner_id)
    
    def get_available_runners(self, capabilities: List[str] = None) -> List[Runner]:
        """获取可用的Runner列表"""
        with self.lock:
            available_runners = []
            
            for runner_id in self.idle_runners:
                runner = self.runners.get(runner_id)
                if not runner:
                    continue
                
                # 检查能力匹配
                if capabilities and not self._check_runner_capabilities(runner, capabilities):
                    continue
                
                # 检查资源限制
                if not self._check_runner_resources(runner):
                    continue
                
                available_runners.append(runner)
            
            # 按负载排序（CPU使用率低的优先）
            available_runners.sort(key=lambda r: r.resource_usage.cpu_percent)
            
            return available_runners
    
    def _check_runner_capabilities(self, runner: Runner, required_capabilities: List[str]) -> bool:
        """检查Runner能力是否满足要求"""
        runner_capabilities = set(runner.capabilities)
        required_capabilities = set(required_capabilities)
        return required_capabilities.issubset(runner_capabilities)
    
    def _check_runner_resources(self, runner: Runner) -> bool:
        """检查Runner资源是否可用"""
        limits = runner.resource_limits
        usage = runner.resource_usage
        
        # 检查CPU使用率
        if usage.cpu_percent >= limits.max_cpu_percent:
            return False
        
        # 检查内存使用量
        if usage.memory_mb >= limits.max_memory_mb:
            return False
        
        # 检查运行时间
        if usage.runtime_seconds >= limits.max_runtime_seconds:
            return False
        
        return True
    
    def get_best_runner(self, capabilities: List[str] = None) -> Optional[Runner]:
        """获取最佳Runner"""
        available_runners = self.get_available_runners(capabilities)
        return available_runners[0] if available_runners else None
    
    def mark_runner_busy(self, runner_id: str, job_id: str) -> bool:
        """标记Runner为忙碌状态"""
        with self.lock:
            runner = self.runners.get(runner_id)
            if not runner or runner.status != RunnerStatus.IDLE:
                return False
            
            old_status = runner.status
            runner.status = RunnerStatus.BUSY
            runner.current_job_id = job_id
            
            self._update_runner_status_sets(runner_id, old_status, RunnerStatus.BUSY)
            
            self.logger.debug(f"Runner {runner_id} 标记为忙碌，执行任务 {job_id}")
            return True
    
    def mark_runner_idle(self, runner_id: str) -> bool:
        """标记Runner为空闲状态"""
        with self.lock:
            runner = self.runners.get(runner_id)
            if not runner:
                return False
            
            old_status = runner.status
            runner.status = RunnerStatus.IDLE
            runner.current_job_id = None
            
            self._update_runner_status_sets(runner_id, old_status, RunnerStatus.IDLE)
            
            self.logger.debug(f"Runner {runner_id} 标记为空闲")
            return True
    
    def mark_runner_offline(self, runner_id: str) -> bool:
        """标记Runner为离线状态"""
        with self.lock:
            runner = self.runners.get(runner_id)
            if not runner:
                return False
            
            old_status = runner.status
            runner.status = RunnerStatus.ERROR
            
            # 从活动集合中移除
            self.active_runners.discard(runner_id)
            self.idle_runners.discard(runner_id)
            self.busy_runners.discard(runner_id)
            self.offline_runners.add(runner_id)
            
            self.logger.warning(f"Runner {runner_id} 标记为离线")
            return True
    
    def get_runner(self, runner_id: str) -> Optional[Runner]:
        """获取Runner"""
        with self.lock:
            return self.runners.get(runner_id)
    
    def get_all_runners(self) -> Dict[str, Runner]:
        """获取所有Runner"""
        with self.lock:
            return self.runners.copy()
    
    def get_runners_by_capability(self, capability: str) -> List[Runner]:
        """根据能力获取Runner列表"""
        with self.lock:
            runner_ids = self.capability_index.get(capability, set())
            return [self.runners[rid] for rid in runner_ids if rid in self.runners]
    
    def cleanup_offline_runners(self, max_offline_hours: int = 24):
        """清理长时间离线的Runner"""
        with self.lock:
            current_time = get_timestamp()
            cutoff_time = current_time - (max_offline_hours * 3600)
            
            runners_to_remove = []
            for runner_id in self.offline_runners:
                runner = self.runners.get(runner_id)
                if runner and runner.last_heartbeat < cutoff_time:
                    runners_to_remove.append(runner_id)
            
            for runner_id in runners_to_remove:
                self._remove_runner(runner_id)
            
            if runners_to_remove:
                self.logger.info(f"清理了 {len(runners_to_remove)} 个长时间离线的Runner")
    
    def _remove_runner(self, runner_id: str):
        """移除Runner"""
        runner = self.runners.pop(runner_id, None)
        if not runner:
            return
        
        # 从所有集合中移除
        self.active_runners.discard(runner_id)
        self.idle_runners.discard(runner_id)
        self.busy_runners.discard(runner_id)
        self.offline_runners.discard(runner_id)
        
        # 清理能力索引
        self._update_capability_index(runner_id, [])
    
    def _notify_runner_shutdown(self, runner_id: str):
        """通知Runner关闭（简化实现）"""
        # 在实际实现中，这里应该发送关闭信号给Runner
        self.logger.info(f"通知Runner关闭: {runner_id}")
    
    def get_status(self) -> Dict:
        """获取管理器状态"""
        with self.lock:
            return {
                "total_runners": len(self.runners),
                "active_runners": len(self.active_runners),
                "idle_runners": len(self.idle_runners),
                "busy_runners": len(self.busy_runners),
                "offline_runners": len(self.offline_runners),
                "capabilities": list(self.capability_index.keys())
            }
    
    def _save_runners(self):
        """保存所有Runner"""
        try:
            runners_data = {runner_id: runner for runner_id, runner in self.runners.items()}
            DataPersistence.save_to_file(runners_data, self.persistence_path)
        except Exception as e:
            self.logger.error(f"保存Runner失败: {e}")
    
    def _save_runner(self, runner: Runner):
        """保存单个Runner（简化实现）"""
        # 在实际应用中，可能需要更高效的增量保存机制
        pass
    
    def _load_runners(self):
        """加载Runner"""
        try:
            import os
            if os.path.exists(self.persistence_path):
                runners_data = DataPersistence.load_from_file(self.persistence_path)
                for runner_id, runner_dict in runners_data.items():
                    # 这里需要将字典转换为Runner对象
                    # 简化实现，实际需要更完善的反序列化
                    pass
        except Exception as e:
            self.logger.error(f"加载Runner失败: {e}")
