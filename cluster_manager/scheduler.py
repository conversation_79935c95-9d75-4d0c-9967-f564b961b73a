"""
集群任务管理平台 - 任务调度器
"""

import threading
import time
from typing import Dict, List, Optional, Tuple
from enum import Enum

from common import (
    get_logger, LoggerMixin, ConfigManager, Job, Runner, JobStatus, RunnerStatus,
    get_timestamp
)


class SchedulingStrategy(Enum):
    """调度策略枚举"""
    FIFO = "fifo"                    # 先进先出
    PRIORITY = "priority"            # 优先级调度
    LOAD_BALANCE = "load_balance"    # 负载均衡
    CAPABILITY_MATCH = "capability_match"  # 能力匹配


class TaskScheduler(LoggerMixin):
    """任务调度器"""

    def __init__(self, config: Confi<PERSON><PERSON><PERSON><PERSON>, job_manager, runner_manager):
        self.config = config
        self.job_manager = job_manager
        self.runner_manager = runner_manager
        self.running = False

        # 调度配置
        scheduler_config = config.get('scheduler', {})
        self.strategy = SchedulingStrategy(scheduler_config.get('strategy', 'priority'))
        self.max_concurrent_jobs = scheduler_config.get('max_concurrent_jobs', 10)
        self.scheduling_interval = scheduler_config.get('scheduling_interval', 5)

        # 调度统计
        self.stats = {
            'total_scheduled': 0,
            'successful_schedules': 0,
            'failed_schedules': 0,
            'last_schedule_time': 0
        }

        # 线程安全锁
        self.lock = threading.RLock()

        self.logger.info(f"任务调度器初始化完成，策略: {self.strategy.value}")

    def start(self):
        """启动调度器"""
        if self.running:
            return

        self.logger.info("启动任务调度器...")
        self.running = True
        self.logger.info("任务调度器启动成功")

    def shutdown(self):
        """关闭调度器"""
        if not self.running:
            return

        self.logger.info("关闭任务调度器...")
        self.running = False
        self.logger.info("任务调度器已关闭")

    def schedule_jobs(self) -> int:
        """执行任务调度，返回成功调度的任务数量"""
        if not self.running:
            return 0

        with self.lock:
            scheduled_count = 0
            start_time = time.time()

            try:
                # 获取当前运行的任务数量
                current_running = len(self.job_manager.running_jobs)

                # 检查是否达到并发限制
                if current_running >= self.max_concurrent_jobs:
                    self.logger.debug(f"已达到最大并发任务数限制: {current_running}/{self.max_concurrent_jobs}")
                    return 0

                # 获取可用的Runner
                available_runners = self.runner_manager.get_available_runners()
                if not available_runners:
                    self.logger.debug("没有可用的Runner")
                    return 0

                # 计算可以调度的任务数量
                max_new_jobs = min(
                    self.max_concurrent_jobs - current_running,
                    len(available_runners)
                )

                # 根据策略调度任务
                if self.strategy == SchedulingStrategy.FIFO:
                    scheduled_count = self._schedule_fifo(max_new_jobs)
                elif self.strategy == SchedulingStrategy.PRIORITY:
                    scheduled_count = self._schedule_priority(max_new_jobs)
                elif self.strategy == SchedulingStrategy.LOAD_BALANCE:
                    scheduled_count = self._schedule_load_balance(max_new_jobs)
                elif self.strategy == SchedulingStrategy.CAPABILITY_MATCH:
                    scheduled_count = self._schedule_capability_match(max_new_jobs)

                # 更新统计信息
                self.stats['total_scheduled'] += scheduled_count
                self.stats['successful_schedules'] += 1 if scheduled_count > 0 else 0
                self.stats['last_schedule_time'] = get_timestamp()

                if scheduled_count > 0:
                    self.logger.info(f"成功调度 {scheduled_count} 个任务")

                return scheduled_count

            except Exception as e:
                self.stats['failed_schedules'] += 1
                self.logger.error(f"任务调度失败: {e}")
                return 0
            finally:
                elapsed = time.time() - start_time
                self.logger.debug(f"调度耗时: {elapsed:.3f}秒")

    def _schedule_fifo(self, max_jobs: int) -> int:
        """先进先出调度策略"""
        scheduled_count = 0

        for _ in range(max_jobs):
            # 获取下一个任务
            job = self.job_manager.get_next_job()
            if not job:
                break

            # 获取最佳Runner
            runner = self.runner_manager.get_best_runner()
            if not runner:
                # 如果没有可用Runner，将任务放回队列
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)
                break

            # 分配任务
            if self._assign_job_to_runner(job, runner):
                scheduled_count += 1
            else:
                # 分配失败，将任务放回队列
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)

        return scheduled_count

    def _schedule_priority(self, max_jobs: int) -> int:
        """优先级调度策略"""
        scheduled_count = 0

        for _ in range(max_jobs):
            # 获取下一个任务（已按优先级排序）
            job = self.job_manager.get_next_job()
            if not job:
                break

            # 获取最佳Runner
            runner = self.runner_manager.get_best_runner()
            if not runner:
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)
                break

            # 分配任务
            if self._assign_job_to_runner(job, runner):
                scheduled_count += 1
            else:
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)

        return scheduled_count

    def _schedule_load_balance(self, max_jobs: int) -> int:
        """负载均衡调度策略"""
        scheduled_count = 0

        # 获取所有可用Runner，按负载排序
        available_runners = self.runner_manager.get_available_runners()
        available_runners.sort(key=lambda r: r.resource_usage.cpu_percent)

        runner_index = 0

        for _ in range(max_jobs):
            job = self.job_manager.get_next_job()
            if not job:
                break

            if runner_index >= len(available_runners):
                break

            runner = available_runners[runner_index]

            # 分配任务
            if self._assign_job_to_runner(job, runner):
                scheduled_count += 1
                runner_index += 1
            else:
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)
                break

        return scheduled_count

    def _schedule_capability_match(self, max_jobs: int) -> int:
        """能力匹配调度策略"""
        scheduled_count = 0

        for _ in range(max_jobs):
            job = self.job_manager.get_next_job()
            if not job:
                break

            # 根据任务需求查找合适的Runner
            required_capabilities = getattr(job, 'required_capabilities', [])
            suitable_runners = self.runner_manager.get_available_runners(required_capabilities)

            if not suitable_runners:
                # 没有合适的Runner，将任务放回队列
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)
                continue

            # 选择负载最低的Runner
            runner = min(suitable_runners, key=lambda r: r.resource_usage.cpu_percent)

            # 分配任务
            if self._assign_job_to_runner(job, runner):
                scheduled_count += 1
            else:
                self.job_manager.update_job_status(job.job_id, JobStatus.PENDING)

        return scheduled_count

    def _assign_job_to_runner(self, job: Job, runner: Runner) -> bool:
        """将任务分配给Runner"""
        try:
            # 标记Runner为忙碌状态
            if not self.runner_manager.mark_runner_busy(runner.runner_id, job.job_id):
                self.logger.warning(f"无法标记Runner {runner.runner_id} 为忙碌状态")
                return False

            # 更新任务状态
            job.runner_id = runner.runner_id
            job.started_at = get_timestamp()

            # 这里应该发送任务给Runner执行
            # 简化实现，实际需要通过API或消息队列发送
            success = self._send_job_to_runner(job, runner)

            if success:
                self.logger.info(f"任务 {job.job_id} 已分配给Runner {runner.runner_id}")
                return True
            else:
                # 发送失败，恢复状态
                self.runner_manager.mark_runner_idle(runner.runner_id)
                return False

        except Exception as e:
            self.logger.error(f"分配任务失败: {e}")
            # 恢复状态
            self.runner_manager.mark_runner_idle(runner.runner_id)
            return False

    def _send_job_to_runner(self, job: Job, runner: Runner) -> bool:
        """发送任务给Runner执行"""
        # 这里是简化实现，实际应该通过HTTP API或消息队列发送任务
        # 现在只是模拟发送成功
        try:
            self.logger.debug(f"发送任务 {job.job_id} 到Runner {runner.runner_id}")
            # 实际实现中，这里应该调用Runner的API
            return True
        except Exception as e:
            self.logger.error(f"发送任务到Runner失败: {e}")
            return False

    def handle_job_completion(self, job_id: str, runner_id: str, success: bool, result: dict = None):
        """处理任务完成"""
        with self.lock:
            # 标记Runner为空闲
            self.runner_manager.mark_runner_idle(runner_id)

            # 更新任务状态
            if success:
                self.job_manager.update_job_status(
                    job_id, JobStatus.COMPLETED, runner_id, result
                )
            else:
                self.job_manager.update_job_status(
                    job_id, JobStatus.FAILED, runner_id, result
                )

            self.logger.info(f"任务 {job_id} 在Runner {runner_id} 上{'成功' if success else '失败'}")

    def handle_runner_failure(self, runner_id: str):
        """处理Runner故障"""
        with self.lock:
            runner = self.runner_manager.get_runner(runner_id)
            if not runner:
                return

            # 如果Runner正在执行任务，需要重新调度
            if runner.current_job_id:
                job = self.job_manager.get_job(runner.current_job_id)
                if job and job.status == JobStatus.RUNNING:
                    # 将任务重新放入待执行队列
                    self.job_manager.update_job_status(
                        job.job_id, JobStatus.PENDING, error_message="Runner故障"
                    )
                    self.logger.warning(f"由于Runner {runner_id} 故障，任务 {job.job_id} 重新调度")

            # 标记Runner为离线
            self.runner_manager.mark_runner_offline(runner_id)

    def get_scheduling_recommendations(self) -> List[Dict]:
        """获取调度建议"""
        recommendations = []

        # 检查资源利用率
        runners = self.runner_manager.get_all_runners()
        if runners:
            avg_cpu = sum(r.resource_usage.cpu_percent for r in runners.values()) / len(runners)
            if avg_cpu > 80:
                recommendations.append({
                    'type': 'resource_warning',
                    'message': f'平均CPU使用率过高: {avg_cpu:.1f}%',
                    'suggestion': '考虑增加更多Runner或优化任务'
                })

        # 检查任务积压
        pending_count = len(self.job_manager.pending_jobs)
        if pending_count > 10:
            recommendations.append({
                'type': 'queue_warning',
                'message': f'待执行任务过多: {pending_count}',
                'suggestion': '考虑增加Runner数量或调整调度策略'
            })

        return recommendations

    def get_status(self) -> Dict:
        """获取调度器状态"""
        with self.lock:
            return {
                'running': self.running,
                'strategy': self.strategy.value,
                'max_concurrent_jobs': self.max_concurrent_jobs,
                'stats': self.stats.copy(),
                'recommendations': self.get_scheduling_recommendations()
            }
