"""
集群任务管理平台 - 系统监控器
"""

import threading
import time
import psutil
from typing import Dict, List, Optional
from collections import deque, defaultdict
from datetime import datetime, timedelta

from common import (
    get_logger, LoggerMixin, ConfigManager, get_timestamp, get_system_info
)


class SystemMonitor(LoggerMixin):
    """系统监控器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.running = False
        
        # 监控配置
        monitor_config = config.get('monitor', {})
        self.collection_interval = monitor_config.get('collection_interval', 30)
        self.history_size = monitor_config.get('history_size', 1000)
        self.alert_thresholds = monitor_config.get('alert_thresholds', {
            'cpu_percent': 90.0,
            'memory_percent': 90.0,
            'disk_percent': 90.0
        })
        
        # 监控数据存储
        self.system_metrics = deque(maxlen=self.history_size)
        self.job_metrics = deque(maxlen=self.history_size)
        self.runner_metrics = deque(maxlen=self.history_size)
        self.alerts = deque(maxlen=100)
        
        # 性能统计
        self.performance_stats = {
            'jobs_per_minute': deque(maxlen=60),
            'avg_job_duration': deque(maxlen=100),
            'runner_utilization': deque(maxlen=60),
            'system_load': deque(maxlen=60)
        }
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info("系统监控器初始化完成")
    
    def start(self):
        """启动监控器"""
        if self.running:
            return
        
        self.logger.info("启动系统监控器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="SystemMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("系统监控器启动成功")
    
    def shutdown(self):
        """关闭监控器"""
        if not self.running:
            return
        
        self.logger.info("关闭系统监控器...")
        
        self.running = False
        self.shutdown_event.set()
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("系统监控器已关闭")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 检查告警条件
                self._check_alerts()
                
                # 等待下一次收集
                self.shutdown_event.wait(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                self.shutdown_event.wait(10)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            timestamp = get_timestamp()
            
            # 系统基础信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 网络信息
            network = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            system_metric = {
                'timestamp': timestamp,
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': memory.used // (1024 * 1024),
                'memory_total_mb': memory.total // (1024 * 1024),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used // (1024 * 1024 * 1024),
                'disk_total_gb': disk.total // (1024 * 1024 * 1024),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'process_count': process_count,
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            }
            
            with self.lock:
                self.system_metrics.append(system_metric)
                
                # 更新性能统计
                self.performance_stats['system_load'].append({
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent
                })
            
            self.logger.debug(f"收集系统指标: CPU {cpu_percent}%, 内存 {memory.percent}%")
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
    
    def record_job_metrics(self, job_id: str, status: str, duration: float = None, 
                          runner_id: str = None):
        """记录任务指标"""
        try:
            timestamp = get_timestamp()
            
            job_metric = {
                'timestamp': timestamp,
                'job_id': job_id,
                'status': status,
                'duration': duration,
                'runner_id': runner_id
            }
            
            with self.lock:
                self.job_metrics.append(job_metric)
                
                # 更新性能统计
                if status == 'completed' and duration:
                    self.performance_stats['avg_job_duration'].append(duration)
                
                # 计算每分钟任务数
                current_minute = int(timestamp // 60)
                if not self.performance_stats['jobs_per_minute'] or \
                   self.performance_stats['jobs_per_minute'][-1]['minute'] != current_minute:
                    self.performance_stats['jobs_per_minute'].append({
                        'minute': current_minute,
                        'count': 1
                    })
                else:
                    self.performance_stats['jobs_per_minute'][-1]['count'] += 1
            
            self.logger.debug(f"记录任务指标: {job_id} - {status}")
            
        except Exception as e:
            self.logger.error(f"记录任务指标失败: {e}")
    
    def record_runner_metrics(self, runner_id: str, status: str, resource_usage: dict):
        """记录Runner指标"""
        try:
            timestamp = get_timestamp()
            
            runner_metric = {
                'timestamp': timestamp,
                'runner_id': runner_id,
                'status': status,
                'cpu_percent': resource_usage.get('cpu_percent', 0),
                'memory_mb': resource_usage.get('memory_mb', 0),
                'runtime_seconds': resource_usage.get('runtime_seconds', 0)
            }
            
            with self.lock:
                self.runner_metrics.append(runner_metric)
            
            self.logger.debug(f"记录Runner指标: {runner_id} - {status}")
            
        except Exception as e:
            self.logger.error(f"记录Runner指标失败: {e}")
    
    def _check_alerts(self):
        """检查告警条件"""
        try:
            if not self.system_metrics:
                return
            
            latest_metric = self.system_metrics[-1]
            timestamp = get_timestamp()
            
            # CPU告警
            if latest_metric['cpu_percent'] > self.alert_thresholds['cpu_percent']:
                self._create_alert(
                    'high_cpu',
                    f"CPU使用率过高: {latest_metric['cpu_percent']:.1f}%",
                    'warning',
                    timestamp
                )
            
            # 内存告警
            if latest_metric['memory_percent'] > self.alert_thresholds['memory_percent']:
                self._create_alert(
                    'high_memory',
                    f"内存使用率过高: {latest_metric['memory_percent']:.1f}%",
                    'warning',
                    timestamp
                )
            
            # 磁盘告警
            if latest_metric['disk_percent'] > self.alert_thresholds['disk_percent']:
                self._create_alert(
                    'high_disk',
                    f"磁盘使用率过高: {latest_metric['disk_percent']:.1f}%",
                    'warning',
                    timestamp
                )
            
        except Exception as e:
            self.logger.error(f"检查告警失败: {e}")
    
    def _create_alert(self, alert_type: str, message: str, severity: str, timestamp: float):
        """创建告警"""
        # 检查是否是重复告警（5分钟内相同类型的告警只记录一次）
        recent_alerts = [alert for alert in self.alerts 
                        if alert['type'] == alert_type and 
                        timestamp - alert['timestamp'] < 300]
        
        if recent_alerts:
            return
        
        alert = {
            'type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': timestamp,
            'acknowledged': False
        }
        
        with self.lock:
            self.alerts.append(alert)
        
        self.logger.warning(f"告警: {message}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        with self.lock:
            if not self.system_metrics:
                return {}
            
            latest = self.system_metrics[-1]
            return {
                'timestamp': latest['timestamp'],
                'cpu_percent': latest['cpu_percent'],
                'memory_percent': latest['memory_percent'],
                'disk_percent': latest['disk_percent'],
                'process_count': latest['process_count'],
                'load_average': latest['load_average']
            }
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        with self.lock:
            stats = {}
            
            # 平均任务执行时间
            if self.performance_stats['avg_job_duration']:
                avg_duration = sum(self.performance_stats['avg_job_duration']) / \
                              len(self.performance_stats['avg_job_duration'])
                stats['avg_job_duration'] = avg_duration
            
            # 每分钟任务数
            if self.performance_stats['jobs_per_minute']:
                recent_jobs = [item['count'] for item in 
                              list(self.performance_stats['jobs_per_minute'])[-10:]]
                stats['jobs_per_minute'] = sum(recent_jobs) / len(recent_jobs)
            
            # 系统负载趋势
            if self.performance_stats['system_load']:
                recent_load = list(self.performance_stats['system_load'])[-10:]
                avg_cpu = sum(item['cpu_percent'] for item in recent_load) / len(recent_load)
                avg_memory = sum(item['memory_percent'] for item in recent_load) / len(recent_load)
                stats['avg_cpu_percent'] = avg_cpu
                stats['avg_memory_percent'] = avg_memory
            
            return stats
    
    def get_alerts(self, unacknowledged_only: bool = False) -> List[Dict]:
        """获取告警列表"""
        with self.lock:
            alerts = list(self.alerts)
            
            if unacknowledged_only:
                alerts = [alert for alert in alerts if not alert['acknowledged']]
            
            return sorted(alerts, key=lambda x: x['timestamp'], reverse=True)
    
    def acknowledge_alert(self, alert_index: int) -> bool:
        """确认告警"""
        with self.lock:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index]['acknowledged'] = True
                return True
            return False
    
    def get_metrics_history(self, metric_type: str, hours: int = 1) -> List[Dict]:
        """获取指标历史数据"""
        with self.lock:
            cutoff_time = get_timestamp() - (hours * 3600)
            
            if metric_type == 'system':
                metrics = self.system_metrics
            elif metric_type == 'job':
                metrics = self.job_metrics
            elif metric_type == 'runner':
                metrics = self.runner_metrics
            else:
                return []
            
            return [metric for metric in metrics if metric['timestamp'] >= cutoff_time]
    
    def get_status(self) -> Dict:
        """获取监控器状态"""
        with self.lock:
            return {
                'running': self.running,
                'collection_interval': self.collection_interval,
                'metrics_count': {
                    'system': len(self.system_metrics),
                    'job': len(self.job_metrics),
                    'runner': len(self.runner_metrics)
                },
                'alerts_count': len(self.alerts),
                'unacknowledged_alerts': len([a for a in self.alerts if not a['acknowledged']]),
                'system_status': self.get_system_status(),
                'performance_stats': self.get_performance_stats()
            }
