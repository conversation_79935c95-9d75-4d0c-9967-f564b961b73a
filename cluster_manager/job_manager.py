"""
集群任务管理平台 - 任务管理器
"""

import threading
import time
from typing import Dict, List, Optional, Set
from collections import defaultdict, deque
from datetime import datetime, timedelta

from common import (
    get_logger, LoggerMixin, ConfigManager, Job, JobStatus,
    generate_id, get_timestamp, DataPersistence
)


class JobManager(LoggerMixin):
    """任务管理器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.running = False
        
        # 任务存储
        self.jobs: Dict[str, Job] = {}
        self.job_queues: Dict[int, deque] = defaultdict(deque)  # 按优先级分组的队列
        self.pending_jobs: Set[str] = set()
        self.running_jobs: Set[str] = set()
        self.completed_jobs: Set[str] = set()
        self.failed_jobs: Set[str] = set()
        
        # 任务依赖关系
        self.job_dependencies: Dict[str, Set[str]] = defaultdict(set)
        self.dependent_jobs: Dict[str, Set[str]] = defaultdict(set)
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 配置参数
        job_config = config.get_section('job')
        self.default_timeout = job_config.get('default_timeout', 1800)
        self.max_retries = job_config.get('max_retries', 3)
        self.retry_delay = job_config.get('retry_delay', 60)
        
        # 持久化配置
        self.persistence_enabled = config.get('database.type') is not None
        self.persistence_path = config.get('database.path', 'data/jobs.json')
        
        self.logger.info("任务管理器初始化完成")
    
    def start(self):
        """启动任务管理器"""
        if self.running:
            return
        
        self.logger.info("启动任务管理器...")
        
        # 加载持久化的任务
        if self.persistence_enabled:
            self._load_jobs()
        
        self.running = True
        self.logger.info("任务管理器启动成功")
    
    def shutdown(self):
        """关闭任务管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭任务管理器...")
        
        # 保存任务状态
        if self.persistence_enabled:
            self._save_jobs()
        
        self.running = False
        self.logger.info("任务管理器已关闭")
    
    def submit_job(self, job: Job) -> str:
        """提交任务"""
        with self.lock:
            # 生成任务ID（如果没有）
            if not job.job_id:
                job.job_id = generate_id("job")
            
            # 设置默认值
            if not job.created_at:
                job.created_at = get_timestamp()
            if job.max_retries is None:
                job.max_retries = self.max_retries
            
            # 验证任务
            self._validate_job(job)
            
            # 存储任务
            self.jobs[job.job_id] = job
            
            # 处理依赖关系
            self._process_dependencies(job)
            
            # 如果没有依赖或依赖已满足，加入待执行队列
            if self._can_schedule_job(job.job_id):
                self._add_to_queue(job)
                self.pending_jobs.add(job.job_id)
            
            self.logger.info(f"任务已提交: {job.job_id} - {job.name}")
            
            # 持久化
            if self.persistence_enabled:
                self._save_job(job)
            
            return job.job_id
    
    def _validate_job(self, job: Job):
        """验证任务"""
        if not job.name:
            raise ValueError("任务名称不能为空")
        if not job.command:
            raise ValueError("任务命令不能为空")
        if job.job_id in self.jobs:
            raise ValueError(f"任务ID已存在: {job.job_id}")
    
    def _process_dependencies(self, job: Job):
        """处理任务依赖关系"""
        if not job.dependencies:
            return
        
        for dep_job_id in job.dependencies:
            if dep_job_id not in self.jobs:
                raise ValueError(f"依赖的任务不存在: {dep_job_id}")
            
            self.job_dependencies[job.job_id].add(dep_job_id)
            self.dependent_jobs[dep_job_id].add(job.job_id)
    
    def _can_schedule_job(self, job_id: str) -> bool:
        """检查任务是否可以调度"""
        dependencies = self.job_dependencies.get(job_id, set())
        
        for dep_job_id in dependencies:
            dep_job = self.jobs.get(dep_job_id)
            if not dep_job or dep_job.status != JobStatus.COMPLETED:
                return False
        
        return True
    
    def _add_to_queue(self, job: Job):
        """将任务添加到队列"""
        priority = job.priority
        self.job_queues[priority].append(job.job_id)
    
    def get_next_job(self, runner_capabilities: List[str] = None) -> Optional[Job]:
        """获取下一个待执行的任务"""
        with self.lock:
            # 按优先级从高到低查找任务
            for priority in sorted(self.job_queues.keys(), reverse=True):
                queue = self.job_queues[priority]
                
                # 在当前优先级队列中查找合适的任务
                for _ in range(len(queue)):
                    job_id = queue.popleft()
                    
                    if job_id not in self.pending_jobs:
                        continue
                    
                    job = self.jobs.get(job_id)
                    if not job:
                        continue
                    
                    # 检查Runner能力匹配
                    if runner_capabilities and not self._check_capabilities(job, runner_capabilities):
                        queue.append(job_id)  # 重新加入队列末尾
                        continue
                    
                    # 找到合适的任务
                    self.pending_jobs.remove(job_id)
                    self.running_jobs.add(job_id)
                    job.status = JobStatus.RUNNING
                    job.started_at = get_timestamp()
                    
                    self.logger.info(f"分配任务: {job_id} - {job.name}")
                    return job
            
            return None
    
    def _check_capabilities(self, job: Job, runner_capabilities: List[str]) -> bool:
        """检查Runner能力是否匹配任务需求"""
        # 简单实现：如果任务没有特殊要求，任何Runner都可以执行
        # 可以根据需要扩展更复杂的匹配逻辑
        return True
    
    def update_job_status(self, job_id: str, status: JobStatus, 
                         runner_id: str = None, result: dict = None, 
                         error_message: str = None) -> bool:
        """更新任务状态"""
        with self.lock:
            job = self.jobs.get(job_id)
            if not job:
                self.logger.warning(f"尝试更新不存在的任务: {job_id}")
                return False
            
            old_status = job.status
            job.status = status
            
            if runner_id:
                job.runner_id = runner_id
            if result:
                job.result = result
            if error_message:
                job.error_message = error_message
            
            # 更新状态集合
            self._update_status_sets(job_id, old_status, status)
            
            # 处理任务完成
            if status == JobStatus.COMPLETED:
                job.completed_at = get_timestamp()
                self._handle_job_completion(job_id)
                self.logger.info(f"任务完成: {job_id} - {job.name}")
            
            # 处理任务失败
            elif status == JobStatus.FAILED:
                job.completed_at = get_timestamp()
                self._handle_job_failure(job)
                self.logger.warning(f"任务失败: {job_id} - {job.name}")
            
            # 持久化
            if self.persistence_enabled:
                self._save_job(job)
            
            return True
    
    def _update_status_sets(self, job_id: str, old_status: JobStatus, new_status: JobStatus):
        """更新状态集合"""
        # 从旧状态集合中移除
        if old_status == JobStatus.PENDING:
            self.pending_jobs.discard(job_id)
        elif old_status == JobStatus.RUNNING:
            self.running_jobs.discard(job_id)
        elif old_status == JobStatus.COMPLETED:
            self.completed_jobs.discard(job_id)
        elif old_status == JobStatus.FAILED:
            self.failed_jobs.discard(job_id)
        
        # 添加到新状态集合
        if new_status == JobStatus.PENDING:
            self.pending_jobs.add(job_id)
        elif new_status == JobStatus.RUNNING:
            self.running_jobs.add(job_id)
        elif new_status == JobStatus.COMPLETED:
            self.completed_jobs.add(job_id)
        elif new_status == JobStatus.FAILED:
            self.failed_jobs.add(job_id)
    
    def _handle_job_completion(self, job_id: str):
        """处理任务完成"""
        # 检查依赖此任务的其他任务
        dependent_job_ids = self.dependent_jobs.get(job_id, set())
        
        for dep_job_id in dependent_job_ids:
            if self._can_schedule_job(dep_job_id):
                dep_job = self.jobs.get(dep_job_id)
                if dep_job and dep_job.status == JobStatus.PENDING:
                    self._add_to_queue(dep_job)
                    self.logger.info(f"依赖任务可以调度: {dep_job_id}")
    
    def _handle_job_failure(self, job: Job):
        """处理任务失败"""
        if job.retry_count < job.max_retries:
            # 重试任务
            job.retry_count += 1
            job.status = JobStatus.PENDING
            job.error_message = None
            
            # 重新加入队列
            self.failed_jobs.discard(job.job_id)
            self.pending_jobs.add(job.job_id)
            self._add_to_queue(job)
            
            self.logger.info(f"任务重试 ({job.retry_count}/{job.max_retries}): {job.job_id}")
        else:
            self.logger.error(f"任务重试次数已达上限: {job.job_id}")
    
    def get_job(self, job_id: str) -> Optional[Job]:
        """获取任务"""
        with self.lock:
            return self.jobs.get(job_id)
    
    def get_jobs(self, status: JobStatus = None, limit: int = None) -> List[Job]:
        """获取任务列表"""
        with self.lock:
            jobs = list(self.jobs.values())
            
            if status:
                jobs = [job for job in jobs if job.status == status]
            
            # 按创建时间排序
            jobs.sort(key=lambda x: x.created_at, reverse=True)
            
            if limit:
                jobs = jobs[:limit]
            
            return jobs
    
    def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        with self.lock:
            job = self.jobs.get(job_id)
            if not job:
                return False
            
            if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                return False
            
            old_status = job.status
            job.status = JobStatus.CANCELLED
            job.completed_at = get_timestamp()
            
            self._update_status_sets(job_id, old_status, JobStatus.CANCELLED)
            
            self.logger.info(f"任务已取消: {job_id}")
            return True
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """清理完成的任务"""
        with self.lock:
            current_time = get_timestamp()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            jobs_to_remove = []
            for job_id, job in self.jobs.items():
                if (job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] and
                    job.completed_at and job.completed_at < cutoff_time):
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                self._remove_job(job_id)
            
            if jobs_to_remove:
                self.logger.info(f"清理了 {len(jobs_to_remove)} 个过期任务")
    
    def _remove_job(self, job_id: str):
        """移除任务"""
        job = self.jobs.pop(job_id, None)
        if not job:
            return
        
        # 从状态集合中移除
        self.pending_jobs.discard(job_id)
        self.running_jobs.discard(job_id)
        self.completed_jobs.discard(job_id)
        self.failed_jobs.discard(job_id)
        
        # 清理依赖关系
        self.job_dependencies.pop(job_id, None)
        self.dependent_jobs.pop(job_id, None)
    
    def get_status(self) -> Dict:
        """获取管理器状态"""
        with self.lock:
            return {
                "total_jobs": len(self.jobs),
                "pending_jobs": len(self.pending_jobs),
                "running_jobs": len(self.running_jobs),
                "completed_jobs": len(self.completed_jobs),
                "failed_jobs": len(self.failed_jobs),
                "queue_sizes": {str(priority): len(queue) 
                              for priority, queue in self.job_queues.items()}
            }
    
    def _save_jobs(self):
        """保存所有任务"""
        try:
            jobs_data = {job_id: job for job_id, job in self.jobs.items()}
            DataPersistence.save_to_file(jobs_data, self.persistence_path)
        except Exception as e:
            self.logger.error(f"保存任务失败: {e}")
    
    def _save_job(self, job: Job):
        """保存单个任务（简化实现）"""
        # 在实际应用中，可能需要更高效的增量保存机制
        pass
    
    def _load_jobs(self):
        """加载任务"""
        try:
            import os
            if os.path.exists(self.persistence_path):
                jobs_data = DataPersistence.load_from_file(self.persistence_path)
                for job_id, job_dict in jobs_data.items():
                    # 这里需要将字典转换为Job对象
                    # 简化实现，实际需要更完善的反序列化
                    pass
        except Exception as e:
            self.logger.error(f"加载任务失败: {e}")
