#!/usr/bin/env python3
"""
集群任务管理平台 - 主入口文件
"""

import sys
import os
import argparse
import signal
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from common import get_logger, ConfigManager
from manager import ClusterManager
from runner import ClusterRunner


def signal_handler(signum, frame):
    """信号处理器"""
    logger = get_logger('cluster.main')
    logger.info(f"接收到信号 {signum}，正在关闭...")
    sys.exit(0)


def run_manager(config_file: str, host: str = None, port: int = None):
    """运行管理程序"""
    logger = get_logger('cluster.main')
    logger.info("启动集群管理程序...")
    
    try:
        # 加载配置
        config = ConfigManager(config_file)
        
        # 覆盖配置参数
        if host:
            config.set('manager.host', host)
        if port:
            config.set('manager.port', port)
        
        # 创建并启动管理程序
        manager = ClusterManager(config)
        manager.start()
        
        logger.info("集群管理程序启动成功")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在关闭...")
        finally:
            manager.shutdown()
            logger.info("集群管理程序已关闭")
            
    except Exception as e:
        logger.error(f"启动管理程序失败: {e}")
        sys.exit(1)


def run_runner(config_file: str, manager_url: str = None, runner_id: str = None):
    """运行Runner程序"""
    logger = get_logger('cluster.main')
    logger.info("启动集群Runner...")
    
    try:
        # 加载配置
        config = ConfigManager(config_file)
        
        # 覆盖配置参数
        if manager_url:
            config.set('manager.url', manager_url)
        if runner_id:
            config.set('runner.id', runner_id)
        
        # 创建并启动Runner
        runner = ClusterRunner(config)
        runner.start()
        
        logger.info("集群Runner启动成功")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在关闭...")
        finally:
            runner.shutdown()
            logger.info("集群Runner已关闭")
            
    except Exception as e:
        logger.error(f"启动Runner失败: {e}")
        sys.exit(1)


def run_tests():
    """运行测试"""
    import unittest
    
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = project_root / 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


def show_status(config_file: str):
    """显示系统状态"""
    logger = get_logger('cluster.main')
    
    try:
        config = ConfigManager(config_file)
        manager_url = f"http://{config.get('manager.host')}:{config.get('manager.port')}"
        
        import requests
        
        # 获取管理程序状态
        response = requests.get(f"{manager_url}/api/v1/status", timeout=5)
        
        if response.status_code == 200:
            status = response.json()
            print("=== 集群状态 ===")
            print(f"管理程序状态: {status.get('status', 'unknown')}")
            print(f"运行时间: {status.get('uptime', 0):.1f} 秒")
            print(f"活跃Runner数: {status.get('active_runners', 0)}")
            print(f"待处理任务数: {status.get('pending_jobs', 0)}")
            print(f"运行中任务数: {status.get('running_jobs', 0)}")
            print(f"已完成任务数: {status.get('completed_jobs', 0)}")
        else:
            print(f"无法获取状态: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到管理程序，请检查管理程序是否运行")
    except Exception as e:
        logger.error(f"获取状态失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='集群任务管理平台')
    parser.add_argument('--config', '-c', default='config.json', help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 管理程序命令
    manager_parser = subparsers.add_parser('manager', help='启动管理程序')
    manager_parser.add_argument('--host', help='绑定主机地址')
    manager_parser.add_argument('--port', type=int, help='绑定端口')
    
    # Runner命令
    runner_parser = subparsers.add_parser('runner', help='启动Runner')
    runner_parser.add_argument('--manager-url', help='管理程序URL')
    runner_parser.add_argument('--runner-id', help='Runner ID')
    
    # 测试命令
    subparsers.add_parser('test', help='运行测试')
    
    # 状态命令
    subparsers.add_parser('status', help='显示系统状态')
    
    # 解析参数
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 执行命令
    if args.command == 'manager':
        run_manager(args.config, args.host, args.port)
    elif args.command == 'runner':
        run_runner(args.config, args.manager_url, args.runner_id)
    elif args.command == 'test':
        success = run_tests()
        sys.exit(0 if success else 1)
    elif args.command == 'status':
        show_status(args.config)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
