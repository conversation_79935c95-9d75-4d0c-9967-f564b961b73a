{"logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/cluster.log", "max_file_size": "10MB", "backup_count": 5}, "manager": {"host": "0.0.0.0", "port": 8080, "max_runners": 100, "heartbeat_timeout": 90, "job_timeout": 3600, "cleanup_interval": 300, "data_dir": "data", "backup_interval": 3600}, "runner": {"id": "", "name": "", "host": "localhost", "port": 0, "manager_url": "http://localhost:8080", "max_concurrent_jobs": 3, "heartbeat_interval": 30, "capabilities": ["python", "shell"], "resource_limits": {"max_cpu_percent": 80.0, "max_memory_mb": 1024, "max_runtime_seconds": 3600}, "working_dir": "workspace"}, "job": {"default_timeout": 1800, "shell_timeout": 3600, "max_retries": 3, "retry_delay": 5, "cleanup_completed_after": 86400}, "communication": {"timeout": 30, "max_retries": 3, "retry_delay": 5, "buffer_size": 8192}, "scheduling": {"policy": "load_balance", "batch_size": 10, "scheduling_interval": 5, "load_balance_strategy": "resource_based"}, "monitoring": {"enabled": true, "collection_interval": 30, "metrics_retention": 86400, "alert_enabled": true, "alert_rules": {"high_cpu": {"enabled": true, "threshold": 90, "cooldown": 300}, "high_memory": {"enabled": true, "threshold": 90, "cooldown": 300}, "high_disk": {"enabled": true, "threshold": 95, "cooldown": 600}}}, "alerting": {"email": {"enabled": false, "smtp_server": "smtp.example.com", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "headers": {"Content-Type": "application/json"}}}, "database": {"type": "sqlite", "path": "data/cluster.db", "backup_enabled": true, "backup_interval": 3600}, "security": {"auth_enabled": false, "api_key": "", "ssl_enabled": false, "ssl_cert": "", "ssl_key": ""}, "performance": {"thread_pool_size": 10, "connection_pool_size": 20, "cache_size": 1000, "gc_interval": 300}}