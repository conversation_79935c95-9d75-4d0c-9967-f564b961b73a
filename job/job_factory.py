"""
集群任务管理平台 - 任务工厂
"""

from typing import Dict, Type, Any, Optional, Callable
from abc import ABC, abstractmethod

from common import get_logger, Job, JobStatus
from .base_job import BaseJob, CommandJob, PythonJob


class JobFactory:
    """任务工厂类"""
    
    def __init__(self):
        self.logger = get_logger('cluster.job.factory')
        self._job_types: Dict[str, Type[BaseJob]] = {}
        self._register_builtin_types()
    
    def _register_builtin_types(self):
        """注册内置任务类型"""
        self.register_job_type('command', CommandJob)
        self.register_job_type('python', PythonJob)
        self.logger.info("已注册内置任务类型")
    
    def register_job_type(self, job_type: str, job_class: Type[BaseJob]):
        """注册任务类型"""
        if not issubclass(job_class, BaseJob):
            raise ValueError(f"任务类必须继承自BaseJob: {job_class}")
        
        self._job_types[job_type] = job_class
        self.logger.info(f"注册任务类型: {job_type} -> {job_class.__name__}")
    
    def unregister_job_type(self, job_type: str):
        """注销任务类型"""
        if job_type in self._job_types:
            del self._job_types[job_type]
            self.logger.info(f"注销任务类型: {job_type}")
    
    def get_registered_types(self) -> Dict[str, Type[BaseJob]]:
        """获取已注册的任务类型"""
        return self._job_types.copy()
    
    def create_job(self, job_config: Job, job_type: str = 'command', **kwargs) -> BaseJob:
        """创建任务实例"""
        if job_type not in self._job_types:
            raise ValueError(f"未知的任务类型: {job_type}")
        
        job_class = self._job_types[job_type]
        
        try:
            # 创建任务实例
            if job_type == 'python':
                # Python任务需要额外的函数参数
                func = kwargs.get('func')
                if not func:
                    raise ValueError("Python任务需要提供func参数")
                
                func_args = kwargs.get('args', ())
                func_kwargs = kwargs.get('kwargs', {})
                job_instance = job_class(job_config, func, *func_args, **func_kwargs)
            else:
                job_instance = job_class(job_config, **kwargs)
            
            self.logger.info(f"创建任务实例: {job_config.job_id} - {job_type}")
            return job_instance
            
        except Exception as e:
            self.logger.error(f"创建任务实例失败: {e}")
            raise
    
    def create_command_job(self, job_config: Job) -> CommandJob:
        """创建命令行任务"""
        return self.create_job(job_config, 'command')
    
    def create_python_job(self, job_config: Job, func: Callable, *args, **kwargs) -> PythonJob:
        """创建Python函数任务"""
        return self.create_job(job_config, 'python', func=func, args=args, kwargs=kwargs)


class JobTemplate:
    """任务模板类"""
    
    def __init__(self, name: str, job_type: str, template_config: Dict[str, Any]):
        self.name = name
        self.job_type = job_type
        self.template_config = template_config
        self.logger = get_logger('cluster.job.template')
    
    def create_job(self, job_id: str, overrides: Dict[str, Any] = None) -> Job:
        """基于模板创建任务配置"""
        # 合并配置
        config = self.template_config.copy()
        if overrides:
            config.update(overrides)
        
        # 创建Job对象
        job = Job(
            job_id=job_id,
            name=config.get('name', self.name),
            description=config.get('description', ''),
            command=config.get('command', ''),
            args=config.get('args', []),
            env=config.get('env', {}),
            working_dir=config.get('working_dir', ''),
            priority=config.get('priority', 0),
            max_retries=config.get('max_retries', 3),
            dependencies=config.get('dependencies', [])
        )
        
        self.logger.info(f"基于模板创建任务: {job_id} - {self.name}")
        return job


class JobTemplateManager:
    """任务模板管理器"""
    
    def __init__(self):
        self.logger = get_logger('cluster.job.template_manager')
        self._templates: Dict[str, JobTemplate] = {}
    
    def register_template(self, template: JobTemplate):
        """注册任务模板"""
        self._templates[template.name] = template
        self.logger.info(f"注册任务模板: {template.name}")
    
    def unregister_template(self, template_name: str):
        """注销任务模板"""
        if template_name in self._templates:
            del self._templates[template_name]
            self.logger.info(f"注销任务模板: {template_name}")
    
    def get_template(self, template_name: str) -> Optional[JobTemplate]:
        """获取任务模板"""
        return self._templates.get(template_name)
    
    def list_templates(self) -> Dict[str, JobTemplate]:
        """列出所有模板"""
        return self._templates.copy()
    
    def create_job_from_template(self, template_name: str, job_id: str, 
                                overrides: Dict[str, Any] = None) -> Optional[Job]:
        """基于模板创建任务"""
        template = self.get_template(template_name)
        if not template:
            self.logger.error(f"模板不存在: {template_name}")
            return None
        
        return template.create_job(job_id, overrides)


# 全局实例
job_factory = JobFactory()
template_manager = JobTemplateManager()


def register_job_type(job_type: str, job_class: Type[BaseJob]):
    """注册任务类型的便捷函数"""
    job_factory.register_job_type(job_type, job_class)


def create_job(job_config: Job, job_type: str = 'command', **kwargs) -> BaseJob:
    """创建任务的便捷函数"""
    return job_factory.create_job(job_config, job_type, **kwargs)


def register_template(name: str, job_type: str, template_config: Dict[str, Any]):
    """注册任务模板的便捷函数"""
    template = JobTemplate(name, job_type, template_config)
    template_manager.register_template(template)


def create_job_from_template(template_name: str, job_id: str, 
                           overrides: Dict[str, Any] = None) -> Optional[Job]:
    """基于模板创建任务的便捷函数"""
    return template_manager.create_job_from_template(template_name, job_id, overrides)


# 预定义模板
def register_builtin_templates():
    """注册内置模板"""
    
    # 数据处理模板
    register_template('data_processing', 'command', {
        'name': '数据处理任务',
        'description': '通用数据处理任务模板',
        'command': 'python',
        'args': ['process_data.py'],
        'env': {'PYTHONPATH': '/app'},
        'priority': 5
    })
    
    # 机器学习训练模板
    register_template('ml_training', 'command', {
        'name': '机器学习训练任务',
        'description': '机器学习模型训练模板',
        'command': 'python',
        'args': ['train_model.py'],
        'env': {'CUDA_VISIBLE_DEVICES': '0'},
        'priority': 10
    })
    
    # 批处理模板
    register_template('batch_processing', 'command', {
        'name': '批处理任务',
        'description': '批量数据处理模板',
        'command': 'bash',
        'args': ['batch_process.sh'],
        'priority': 3
    })
    
    # 测试任务模板
    register_template('test_job', 'command', {
        'name': '测试任务',
        'description': '用于测试的简单任务',
        'command': 'echo',
        'args': ['Hello, Cluster!'],
        'priority': 1
    })


# 自动注册内置模板
register_builtin_templates()
