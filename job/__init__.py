# 集群任务管理平台 - 任务模块
__version__ = "0.1.0"

# 导入核心组件
from .base_job import <PERSON><PERSON>ob, CommandJob, PythonJob, JobResult
from .job_factory import (
    JobFactory, JobTemplate, JobTemplateManager,
    job_factory, template_manager,
    register_job_type, create_job, register_template, create_job_from_template
)
from .status_reporter import StatusReporter, JobStatusTracker, StatusReport

__all__ = [
    # 基础任务类
    'BaseJob', 'CommandJob', 'PythonJob', 'JobResult',

    # 任务工厂
    'JobFactory', 'JobTemplate', 'JobTemplateManager',
    'job_factory', 'template_manager',
    'register_job_type', 'create_job', 'register_template', 'create_job_from_template',

    # 状态报告
    'StatusReporter', 'JobStatusTracker', 'StatusReport'
]
