"""
集群任务管理平台 - 任务状态报告器
"""

import threading
import time
import requests
import json
from typing import Dict, List, Optional, Callable, Any
from queue import Queue, Empty
from dataclasses import dataclass

from common import (
    get_logger, LoggerMixin, JobStatus, get_timestamp, retry_on_exception
)


@dataclass
class StatusReport:
    """状态报告数据类"""
    job_id: str
    status: JobStatus
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = get_timestamp()


class StatusReporter(LoggerMixin):
    """任务状态报告器"""
    
    def __init__(self, manager_url: str, runner_id: str):
        self.manager_url = manager_url.rstrip('/')
        self.runner_id = runner_id
        self.running = False
        
        # 报告队列
        self.report_queue = Queue()
        
        # 配置参数
        self.batch_size = 10
        self.report_interval = 5  # 秒
        self.max_retries = 3
        self.timeout = 30
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'ClusterJob/1.0'
        })
        
        # 报告线程
        self.report_thread = None
        self.shutdown_event = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_reports': 0,
            'successful_reports': 0,
            'failed_reports': 0,
            'last_report_time': 0
        }
        
        self.logger.info(f"状态报告器初始化完成: {manager_url}")
    
    def start(self):
        """启动状态报告器"""
        if self.running:
            return
        
        self.logger.info("启动状态报告器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动报告线程
        self.report_thread = threading.Thread(
            target=self._report_loop,
            name="StatusReporter",
            daemon=True
        )
        self.report_thread.start()
        
        self.logger.info("状态报告器启动成功")
    
    def shutdown(self):
        """关闭状态报告器"""
        if not self.running:
            return
        
        self.logger.info("关闭状态报告器...")
        
        self.running = False
        self.shutdown_event.set()
        
        # 等待报告线程结束
        if self.report_thread and self.report_thread.is_alive():
            self.report_thread.join(timeout=10)
        
        # 关闭HTTP会话
        self.session.close()
        
        self.logger.info("状态报告器已关闭")
    
    def report_status(self, job_id: str, status: JobStatus, progress: float = 0.0,
                     message: str = "", result: Dict[str, Any] = None,
                     error_message: str = None):
        """报告任务状态"""
        if not self.running:
            self.logger.warning("状态报告器未运行，忽略状态报告")
            return
        
        report = StatusReport(
            job_id=job_id,
            status=status,
            progress=progress,
            message=message,
            result=result,
            error_message=error_message
        )
        
        try:
            self.report_queue.put(report, timeout=1)
            self.logger.debug(f"状态报告已入队: {job_id} - {status.value}")
        except Exception as e:
            self.logger.error(f"状态报告入队失败: {e}")
    
    def _report_loop(self):
        """状态报告循环"""
        self.logger.info("状态报告循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 收集报告
                reports = self._collect_reports()
                
                if reports:
                    # 发送报告
                    self._send_reports(reports)
                
                # 等待下一次报告
                self.shutdown_event.wait(self.report_interval)
                
            except Exception as e:
                self.logger.error(f"状态报告循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _collect_reports(self) -> List[StatusReport]:
        """收集待发送的报告"""
        reports = []
        
        try:
            # 收集报告，最多收集batch_size个
            for _ in range(self.batch_size):
                try:
                    report = self.report_queue.get(timeout=0.1)
                    reports.append(report)
                except Empty:
                    break
        except Exception as e:
            self.logger.error(f"收集报告失败: {e}")
        
        return reports
    
    def _send_reports(self, reports: List[StatusReport]):
        """发送状态报告"""
        for report in reports:
            try:
                success = self._send_single_report(report)
                
                # 更新统计
                self.stats['total_reports'] += 1
                if success:
                    self.stats['successful_reports'] += 1
                else:
                    self.stats['failed_reports'] += 1
                
                self.stats['last_report_time'] = get_timestamp()
                
            except Exception as e:
                self.logger.error(f"发送状态报告失败: {e}")
                self.stats['failed_reports'] += 1
    
    @retry_on_exception(max_retries=3, delay=1.0)
    def _send_single_report(self, report: StatusReport) -> bool:
        """发送单个状态报告"""
        try:
            # 构建请求数据
            data = {
                'status': report.status.value,
                'runner_id': self.runner_id,
                'timestamp': report.timestamp
            }
            
            if report.progress > 0:
                data['progress'] = report.progress
            if report.message:
                data['message'] = report.message
            if report.result:
                data['result'] = report.result
            if report.error_message:
                data['error_message'] = report.error_message
            
            # 发送请求
            url = f"{self.manager_url}/api/v1/jobs/{report.job_id}/status"
            response = self.session.post(
                url,
                data=json.dumps(data),
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                self.logger.debug(f"状态报告发送成功: {report.job_id} - {report.status.value}")
                return True
            else:
                self.logger.error(f"状态报告发送失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送状态报告异常: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.report_queue.qsize()
    
    def clear_queue(self):
        """清空队列"""
        while not self.report_queue.empty():
            try:
                self.report_queue.get_nowait()
            except Empty:
                break
        
        self.logger.info("状态报告队列已清空")


class JobStatusTracker(LoggerMixin):
    """任务状态跟踪器"""
    
    def __init__(self, status_reporter: StatusReporter):
        self.status_reporter = status_reporter
        self.job_states: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        
        self.logger.info("任务状态跟踪器初始化完成")
    
    def track_job(self, job_id: str, initial_status: JobStatus = JobStatus.PENDING):
        """开始跟踪任务"""
        with self.lock:
            self.job_states[job_id] = {
                'status': initial_status,
                'progress': 0.0,
                'message': '',
                'start_time': get_timestamp(),
                'last_update': get_timestamp()
            }
            
            # 报告初始状态
            self.status_reporter.report_status(job_id, initial_status)
            
            self.logger.info(f"开始跟踪任务: {job_id}")
    
    def update_status(self, job_id: str, status: JobStatus, progress: float = None,
                     message: str = None, result: Dict[str, Any] = None,
                     error_message: str = None):
        """更新任务状态"""
        with self.lock:
            if job_id not in self.job_states:
                self.logger.warning(f"尝试更新未跟踪的任务: {job_id}")
                return
            
            job_state = self.job_states[job_id]
            
            # 更新状态
            old_status = job_state['status']
            job_state['status'] = status
            job_state['last_update'] = get_timestamp()
            
            if progress is not None:
                job_state['progress'] = progress
            if message is not None:
                job_state['message'] = message
            
            # 报告状态变更
            self.status_reporter.report_status(
                job_id, status, job_state['progress'], 
                job_state['message'], result, error_message
            )
            
            self.logger.info(f"任务状态更新: {job_id} - {old_status.value} -> {status.value}")
    
    def update_progress(self, job_id: str, progress: float, message: str = ""):
        """更新任务进度"""
        with self.lock:
            if job_id not in self.job_states:
                self.logger.warning(f"尝试更新未跟踪的任务进度: {job_id}")
                return
            
            job_state = self.job_states[job_id]
            job_state['progress'] = progress
            job_state['message'] = message
            job_state['last_update'] = get_timestamp()
            
            # 报告进度更新
            self.status_reporter.report_status(
                job_id, job_state['status'], progress, message
            )
            
            self.logger.debug(f"任务进度更新: {job_id} - {progress:.1%}")
    
    def complete_job(self, job_id: str, result: Dict[str, Any] = None):
        """完成任务"""
        self.update_status(job_id, JobStatus.COMPLETED, 1.0, "任务完成", result)
    
    def fail_job(self, job_id: str, error_message: str):
        """任务失败"""
        self.update_status(job_id, JobStatus.FAILED, error_message=error_message)
    
    def cancel_job(self, job_id: str):
        """取消任务"""
        self.update_status(job_id, JobStatus.CANCELLED, message="任务已取消")
    
    def stop_tracking(self, job_id: str):
        """停止跟踪任务"""
        with self.lock:
            if job_id in self.job_states:
                del self.job_states[job_id]
                self.logger.info(f"停止跟踪任务: {job_id}")
    
    def get_job_state(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            return self.job_states.get(job_id, {}).copy()
    
    def get_all_job_states(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        with self.lock:
            return {job_id: state.copy() for job_id, state in self.job_states.items()}
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """清理已完成的任务"""
        with self.lock:
            current_time = get_timestamp()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            jobs_to_remove = []
            for job_id, state in self.job_states.items():
                if (state['status'] in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] and
                    state['last_update'] < cutoff_time):
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self.job_states[job_id]
            
            if jobs_to_remove:
                self.logger.info(f"清理了 {len(jobs_to_remove)} 个已完成的任务状态")
