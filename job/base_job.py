"""
集群任务管理平台 - 基础任务类
"""

import abc
import time
import traceback
from typing import Dict, Any, Optional, List
from enum import Enum

from common import (
    get_logger, LoggerMixin, Job, JobStatus, get_timestamp
)


class JobResult(Enum):
    """任务结果枚举"""
    SUCCESS = "success"
    FAILURE = "failure"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class BaseJob(LoggerMixin, abc.ABC):
    """基础任务类"""
    
    def __init__(self, job_config: Job):
        self.job_config = job_config
        self.job_id = job_config.job_id
        self.name = job_config.name
        self.description = job_config.description
        
        # 执行状态
        self.status = JobStatus.PENDING
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.result: Optional[Dict[str, Any]] = None
        self.error_message: Optional[str] = None
        
        # 进度跟踪
        self.progress = 0.0  # 0.0 - 1.0
        self.progress_message = ""
        
        # 状态回调
        self.status_callbacks: List[callable] = []
        
        self.logger.info(f"任务初始化: {self.job_id} - {self.name}")
    
    @abc.abstractmethod
    def execute(self) -> Dict[str, Any]:
        """执行任务的具体逻辑，子类必须实现"""
        pass
    
    def run(self) -> Dict[str, Any]:
        """运行任务"""
        try:
            self.logger.info(f"开始执行任务: {self.job_id}")
            
            # 更新状态
            self._update_status(JobStatus.RUNNING)
            self.start_time = get_timestamp()
            
            # 执行前置检查
            if not self.pre_execute():
                raise RuntimeError("前置检查失败")
            
            # 执行任务
            result = self.execute()
            
            # 执行后置处理
            self.post_execute(result)
            
            # 更新状态
            self.result = result
            self._update_status(JobStatus.COMPLETED)
            
            self.logger.info(f"任务执行成功: {self.job_id}")
            return {
                'success': True,
                'result': result,
                'job_id': self.job_id,
                'duration': self.get_duration()
            }
            
        except Exception as e:
            # 处理异常
            self.error_message = str(e)
            self._update_status(JobStatus.FAILED)
            
            self.logger.error(f"任务执行失败: {self.job_id} - {e}")
            self.logger.debug(f"异常详情: {traceback.format_exc()}")
            
            return {
                'success': False,
                'error': str(e),
                'job_id': self.job_id,
                'duration': self.get_duration()
            }
        finally:
            self.end_time = get_timestamp()
    
    def pre_execute(self) -> bool:
        """执行前的准备工作，子类可以重写"""
        self.logger.debug(f"执行前置检查: {self.job_id}")
        return True
    
    def post_execute(self, result: Dict[str, Any]):
        """执行后的清理工作，子类可以重写"""
        self.logger.debug(f"执行后置处理: {self.job_id}")
    
    def cancel(self):
        """取消任务"""
        if self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            self.logger.warning(f"任务已完成，无法取消: {self.job_id}")
            return
        
        self._update_status(JobStatus.CANCELLED)
        self.logger.info(f"任务已取消: {self.job_id}")
    
    def update_progress(self, progress: float, message: str = ""):
        """更新任务进度"""
        self.progress = max(0.0, min(1.0, progress))
        self.progress_message = message
        
        self.logger.debug(f"任务进度更新: {self.job_id} - {self.progress:.1%} - {message}")
        
        # 通知状态回调
        for callback in self.status_callbacks:
            try:
                callback(self.job_id, 'progress', {
                    'progress': self.progress,
                    'message': message
                })
            except Exception as e:
                self.logger.error(f"进度回调失败: {e}")
    
    def _update_status(self, status: JobStatus):
        """更新任务状态"""
        old_status = self.status
        self.status = status
        
        self.logger.info(f"任务状态变更: {self.job_id} - {old_status.value} -> {status.value}")
        
        # 通知状态回调
        for callback in self.status_callbacks:
            try:
                callback(self.job_id, 'status', {
                    'old_status': old_status.value,
                    'new_status': status.value,
                    'timestamp': get_timestamp()
                })
            except Exception as e:
                self.logger.error(f"状态回调失败: {e}")
    
    def add_status_callback(self, callback: callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
    
    def remove_status_callback(self, callback: callable):
        """移除状态回调函数"""
        if callback in self.status_callbacks:
            self.status_callbacks.remove(callback)
    
    def get_duration(self) -> float:
        """获取任务执行时长"""
        if not self.start_time:
            return 0.0
        
        end_time = self.end_time or get_timestamp()
        return end_time - self.start_time
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取任务状态信息"""
        return {
            'job_id': self.job_id,
            'name': self.name,
            'description': self.description,
            'status': self.status.value,
            'progress': self.progress,
            'progress_message': self.progress_message,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.get_duration(),
            'result': self.result,
            'error_message': self.error_message
        }
    
    def is_running(self) -> bool:
        """检查任务是否正在运行"""
        return self.status == JobStatus.RUNNING
    
    def is_completed(self) -> bool:
        """检查任务是否已完成"""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]
    
    def is_successful(self) -> bool:
        """检查任务是否成功完成"""
        return self.status == JobStatus.COMPLETED
    
    def __str__(self) -> str:
        return f"Job({self.job_id}, {self.name}, {self.status.value})"
    
    def __repr__(self) -> str:
        return self.__str__()


class CommandJob(BaseJob):
    """命令行任务"""
    
    def __init__(self, job_config: Job):
        super().__init__(job_config)
        self.command = job_config.command
        self.args = job_config.args or []
        self.env = job_config.env or {}
        self.working_dir = job_config.working_dir
    
    def execute(self) -> Dict[str, Any]:
        """执行命令行任务"""
        import subprocess
        import os
        
        # 准备执行环境
        env = os.environ.copy()
        env.update(self.env)
        
        # 设置工作目录
        cwd = self.working_dir or os.getcwd()
        
        # 构建命令
        cmd = [self.command] + self.args
        
        self.logger.info(f"执行命令: {' '.join(cmd)}")
        self.update_progress(0.1, "开始执行命令")
        
        try:
            # 执行命令
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.update_progress(0.5, "命令执行中")
            
            # 等待完成
            stdout, stderr = process.communicate()
            exit_code = process.returncode
            
            self.update_progress(1.0, "命令执行完成")
            
            # 返回结果
            return {
                'exit_code': exit_code,
                'stdout': stdout,
                'stderr': stderr,
                'command': ' '.join(cmd),
                'success': exit_code == 0
            }
            
        except Exception as e:
            self.logger.error(f"命令执行异常: {e}")
            raise


class PythonJob(BaseJob):
    """Python函数任务"""
    
    def __init__(self, job_config: Job, func: callable, *args, **kwargs):
        super().__init__(job_config)
        self.func = func
        self.func_args = args
        self.func_kwargs = kwargs
    
    def execute(self) -> Dict[str, Any]:
        """执行Python函数"""
        self.logger.info(f"执行Python函数: {self.func.__name__}")
        self.update_progress(0.1, f"开始执行函数 {self.func.__name__}")
        
        try:
            # 执行函数
            result = self.func(*self.func_args, **self.func_kwargs)
            
            self.update_progress(1.0, "函数执行完成")
            
            return {
                'function': self.func.__name__,
                'result': result,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"函数执行异常: {e}")
            raise
