"""
集群任务管理平台 - 通用模块测试
"""

import unittest
import tempfile
import os
import json
import time
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.models import Job, Runner, JobStatus, RunnerStatus, ResourceLimits, ResourceUsage
from common.utils import generate_id, get_timestamp, get_hostname, get_local_ip
from common.config import ConfigManager
from common.logger import ClusterLogger
from common.serialization import MessageSerializer


class TestModels(unittest.TestCase):
    """测试数据模型"""
    
    def test_job_creation(self):
        """测试任务创建"""
        job = Job(
            job_id="test-job-1",
            name="测试任务",
            description="这是一个测试任务",
            command="echo",
            args=["hello", "world"],
            env={"TEST": "value"},
            priority=5
        )
        
        self.assertEqual(job.job_id, "test-job-1")
        self.assertEqual(job.name, "测试任务")
        self.assertEqual(job.command, "echo")
        self.assertEqual(job.args, ["hello", "world"])
        self.assertEqual(job.env, {"TEST": "value"})
        self.assertEqual(job.priority, 5)
        self.assertEqual(job.status, JobStatus.PENDING)
    
    def test_runner_creation(self):
        """测试Runner创建"""
        resource_limits = ResourceLimits(
            max_cpu_percent=80.0,
            max_memory_mb=1024,
            max_runtime_seconds=3600
        )
        
        runner = Runner(
            runner_id="test-runner-1",
            name="测试Runner",
            host="localhost",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=resource_limits,
            capabilities=["python", "docker"]
        )
        
        self.assertEqual(runner.runner_id, "test-runner-1")
        self.assertEqual(runner.name, "测试Runner")
        self.assertEqual(runner.host, "localhost")
        self.assertEqual(runner.port, 8080)
        self.assertEqual(runner.status, RunnerStatus.IDLE)
        self.assertEqual(runner.capabilities, ["python", "docker"])
        self.assertEqual(runner.resource_limits.max_cpu_percent, 80.0)
    
    def test_job_status_transitions(self):
        """测试任务状态转换"""
        job = Job(job_id="test", name="test", command="echo")
        
        # 初始状态
        self.assertEqual(job.status, JobStatus.PENDING)
        
        # 状态转换
        job.status = JobStatus.RUNNING
        self.assertEqual(job.status, JobStatus.RUNNING)
        
        job.status = JobStatus.COMPLETED
        self.assertEqual(job.status, JobStatus.COMPLETED)


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    def test_generate_id(self):
        """测试ID生成"""
        id1 = generate_id("test")
        id2 = generate_id("test")
        
        # ID应该不同
        self.assertNotEqual(id1, id2)
        
        # ID应该包含前缀
        self.assertTrue(id1.startswith("test-"))
        self.assertTrue(id2.startswith("test-"))
    
    def test_get_timestamp(self):
        """测试时间戳获取"""
        timestamp1 = get_timestamp()
        time.sleep(0.1)
        timestamp2 = get_timestamp()
        
        # 时间戳应该递增
        self.assertGreater(timestamp2, timestamp1)
        
        # 时间戳应该是浮点数
        self.assertIsInstance(timestamp1, float)
    
    def test_get_hostname(self):
        """测试主机名获取"""
        hostname = get_hostname()
        
        # 主机名应该是字符串且不为空
        self.assertIsInstance(hostname, str)
        self.assertGreater(len(hostname), 0)
    
    def test_get_local_ip(self):
        """测试本地IP获取"""
        ip = get_local_ip()
        
        # IP应该是字符串且不为空
        self.assertIsInstance(ip, str)
        self.assertGreater(len(ip), 0)


class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_config = {
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "manager": {
                "host": "localhost",
                "port": 8080
            },
            "runner": {
                "max_concurrent_jobs": 3,
                "heartbeat_interval": 30
            }
        }
        
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_config, self.temp_file)
        self.temp_file.close()
    
    def tearDown(self):
        """清理测试环境"""
        os.unlink(self.temp_file.name)
    
    def test_load_config(self):
        """测试配置加载"""
        config = ConfigManager(self.temp_file.name)
        
        # 检查配置是否正确加载
        self.assertEqual(config.get('logging.level'), 'INFO')
        self.assertEqual(config.get('manager.host'), 'localhost')
        self.assertEqual(config.get('manager.port'), 8080)
        self.assertEqual(config.get('runner.max_concurrent_jobs'), 3)
    
    def test_get_section(self):
        """测试获取配置段"""
        config = ConfigManager(self.temp_file.name)
        
        logging_config = config.get_section('logging')
        self.assertEqual(logging_config['level'], 'INFO')
        
        manager_config = config.get_section('manager')
        self.assertEqual(manager_config['host'], 'localhost')
        self.assertEqual(manager_config['port'], 8080)
    
    def test_set_config(self):
        """测试设置配置"""
        config = ConfigManager(self.temp_file.name)
        
        # 设置新值
        config.set('manager.port', 9090)
        self.assertEqual(config.get('manager.port'), 9090)
        
        # 设置嵌套值
        config.set('new.nested.value', 'test')
        self.assertEqual(config.get('new.nested.value'), 'test')
    
    def test_default_values(self):
        """测试默认值"""
        config = ConfigManager(self.temp_file.name)
        
        # 不存在的键应该返回默认值
        self.assertEqual(config.get('nonexistent.key', 'default'), 'default')
        self.assertIsNone(config.get('nonexistent.key'))
    
    def test_validation(self):
        """测试配置验证"""
        config = ConfigManager(self.temp_file.name)
        
        # 正常配置应该通过验证
        self.assertTrue(config.validate())


class TestMessageSerializer(unittest.TestCase):
    """测试消息序列化"""
    
    def test_job_serialization(self):
        """测试任务序列化"""
        job = Job(
            job_id="test-job",
            name="测试任务",
            command="echo",
            args=["hello"],
            env={"TEST": "value"},
            priority=5
        )
        
        # 序列化
        serialized = MessageSerializer.serialize_job(job)
        self.assertIsInstance(serialized, str)
        
        # 反序列化
        deserialized = MessageSerializer.deserialize_job(serialized)
        
        # 检查反序列化结果
        self.assertEqual(deserialized.job_id, job.job_id)
        self.assertEqual(deserialized.name, job.name)
        self.assertEqual(deserialized.command, job.command)
        self.assertEqual(deserialized.args, job.args)
        self.assertEqual(deserialized.env, job.env)
        self.assertEqual(deserialized.priority, job.priority)
    
    def test_runner_serialization(self):
        """测试Runner序列化"""
        resource_limits = ResourceLimits(
            max_cpu_percent=80.0,
            max_memory_mb=1024,
            max_runtime_seconds=3600
        )
        
        runner = Runner(
            runner_id="test-runner",
            name="测试Runner",
            host="localhost",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=resource_limits,
            capabilities=["python"]
        )
        
        # 序列化
        serialized = MessageSerializer.serialize_runner(runner)
        self.assertIsInstance(serialized, str)
        
        # 反序列化
        deserialized = MessageSerializer.deserialize_runner(serialized)
        
        # 检查反序列化结果
        self.assertEqual(deserialized.runner_id, runner.runner_id)
        self.assertEqual(deserialized.name, runner.name)
        self.assertEqual(deserialized.host, runner.host)
        self.assertEqual(deserialized.port, runner.port)
        self.assertEqual(deserialized.status, runner.status)
        self.assertEqual(deserialized.capabilities, runner.capabilities)
    
    def test_invalid_serialization(self):
        """测试无效序列化"""
        # 测试无效JSON
        with self.assertRaises(Exception):
            MessageSerializer.deserialize_job("invalid json")
        
        with self.assertRaises(Exception):
            MessageSerializer.deserialize_runner("invalid json")


class TestLogger(unittest.TestCase):
    """测试日志系统"""
    
    def test_logger_creation(self):
        """测试日志器创建"""
        logger = ClusterLogger.get_logger('test.logger')
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, 'test.logger')
    
    def test_logger_with_config(self):
        """测试带配置的日志器"""
        config = {
            'level': 'DEBUG',
            'format': '%(name)s - %(levelname)s - %(message)s'
        }
        
        logger = ClusterLogger.get_logger('test.config', config)
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.level, 10)  # DEBUG level
    
    @patch('common.logger.logging.FileHandler')
    def test_file_logging(self, mock_file_handler):
        """测试文件日志"""
        config = {
            'level': 'INFO',
            'file': '/tmp/test.log'
        }
        
        logger = ClusterLogger.get_logger('test.file', config)
        
        # 应该创建文件处理器
        mock_file_handler.assert_called_once()


if __name__ == '__main__':
    unittest.main()
