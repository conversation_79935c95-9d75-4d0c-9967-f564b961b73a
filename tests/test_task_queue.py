"""
集群任务管理平台 - 任务队列测试
"""

import unittest
import time
from unittest.mock import Mock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.task_queue import TaskQueue, QueueManager, QueueType, QueuedJob
from common.models import Job, JobStatus


class TestTaskQueue(unittest.TestCase):
    """测试任务队列"""
    
    def setUp(self):
        """设置测试环境"""
        self.queue = TaskQueue("test-queue", QueueType.PRIORITY, max_size=10)
        
        # 创建测试任务
        self.job1 = Job(job_id="job-1", name="任务1", command="echo", priority=5)
        self.job2 = Job(job_id="job-2", name="任务2", command="echo", priority=10)
        self.job3 = Job(job_id="job-3", name="任务3", command="echo", priority=1)
    
    def test_enqueue_dequeue(self):
        """测试入队出队"""
        # 入队
        self.assertTrue(self.queue.enqueue(self.job1, priority=5))
        self.assertTrue(self.queue.enqueue(self.job2, priority=10))
        self.assertTrue(self.queue.enqueue(self.job3, priority=1))
        
        # 检查队列大小
        self.assertEqual(self.queue.size(), 3)
        
        # 出队（应该按优先级顺序）
        job = self.queue.dequeue()
        self.assertEqual(job.job_id, "job-2")  # 优先级最高
        
        job = self.queue.dequeue()
        self.assertEqual(job.job_id, "job-1")  # 优先级中等
        
        job = self.queue.dequeue()
        self.assertEqual(job.job_id, "job-3")  # 优先级最低
        
        # 队列应该为空
        self.assertTrue(self.queue.is_empty())
        self.assertIsNone(self.queue.dequeue())
    
    def test_fifo_queue(self):
        """测试FIFO队列"""
        fifo_queue = TaskQueue("fifo-queue", QueueType.FIFO)
        
        # 入队
        fifo_queue.enqueue(self.job1)
        fifo_queue.enqueue(self.job2)
        fifo_queue.enqueue(self.job3)
        
        # 出队（应该按入队顺序）
        job = fifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-1")
        
        job = fifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-2")
        
        job = fifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-3")
    
    def test_lifo_queue(self):
        """测试LIFO队列"""
        lifo_queue = TaskQueue("lifo-queue", QueueType.LIFO)
        
        # 入队
        lifo_queue.enqueue(self.job1)
        lifo_queue.enqueue(self.job2)
        lifo_queue.enqueue(self.job3)
        
        # 出队（应该按后进先出顺序）
        job = lifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-3")
        
        job = lifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-2")
        
        job = lifo_queue.dequeue()
        self.assertEqual(job.job_id, "job-1")
    
    def test_queue_size_limit(self):
        """测试队列大小限制"""
        small_queue = TaskQueue("small-queue", QueueType.FIFO, max_size=2)
        
        # 添加任务直到达到限制
        self.assertTrue(small_queue.enqueue(self.job1))
        self.assertTrue(small_queue.enqueue(self.job2))
        
        # 队列已满，应该拒绝新任务
        self.assertFalse(small_queue.enqueue(self.job3))
        self.assertTrue(small_queue.is_full())
    
    def test_duplicate_job(self):
        """测试重复任务"""
        # 添加任务
        self.assertTrue(self.queue.enqueue(self.job1))
        
        # 尝试添加相同任务
        self.assertFalse(self.queue.enqueue(self.job1))
        
        # 队列大小应该仍为1
        self.assertEqual(self.queue.size(), 1)
    
    def test_contains_and_remove(self):
        """测试包含和移除操作"""
        # 添加任务
        self.queue.enqueue(self.job1)
        self.queue.enqueue(self.job2)
        
        # 检查包含
        self.assertTrue(self.queue.contains("job-1"))
        self.assertTrue(self.queue.contains("job-2"))
        self.assertFalse(self.queue.contains("job-3"))
        
        # 移除任务
        self.assertTrue(self.queue.remove("job-1"))
        self.assertFalse(self.queue.contains("job-1"))
        self.assertEqual(self.queue.size(), 1)
        
        # 尝试移除不存在的任务
        self.assertFalse(self.queue.remove("job-3"))
    
    def test_peek(self):
        """测试查看队首"""
        # 空队列
        self.assertIsNone(self.queue.peek())
        
        # 添加任务
        self.queue.enqueue(self.job1, priority=5)
        self.queue.enqueue(self.job2, priority=10)
        
        # 查看队首（不出队）
        job = self.queue.peek()
        self.assertEqual(job.job_id, "job-2")  # 优先级最高
        
        # 队列大小不变
        self.assertEqual(self.queue.size(), 2)
    
    def test_clear(self):
        """测试清空队列"""
        # 添加任务
        self.queue.enqueue(self.job1)
        self.queue.enqueue(self.job2)
        
        # 清空队列
        self.queue.clear()
        
        # 队列应该为空
        self.assertTrue(self.queue.is_empty())
        self.assertEqual(self.queue.size(), 0)
    
    def test_get_jobs(self):
        """测试获取所有任务"""
        # 添加任务
        self.queue.enqueue(self.job1)
        self.queue.enqueue(self.job2)
        
        # 获取所有任务
        jobs = self.queue.get_jobs()
        self.assertEqual(len(jobs), 2)
        
        job_ids = [job.job_id for job in jobs]
        self.assertIn("job-1", job_ids)
        self.assertIn("job-2", job_ids)
    
    def test_callbacks(self):
        """测试回调函数"""
        enqueue_called = False
        dequeue_called = False
        
        def enqueue_callback(job, priority):
            nonlocal enqueue_called
            enqueue_called = True
            self.assertEqual(job.job_id, "job-1")
            self.assertEqual(priority, 5)
        
        def dequeue_callback(job):
            nonlocal dequeue_called
            dequeue_called = True
            self.assertEqual(job.job_id, "job-1")
        
        # 添加回调
        self.queue.add_enqueue_callback(enqueue_callback)
        self.queue.add_dequeue_callback(dequeue_callback)
        
        # 执行操作
        self.queue.enqueue(self.job1, priority=5)
        self.queue.dequeue()
        
        # 检查回调是否被调用
        self.assertTrue(enqueue_called)
        self.assertTrue(dequeue_called)
    
    def test_stats(self):
        """测试统计信息"""
        # 初始统计
        stats = self.queue.get_stats()
        self.assertEqual(stats['total_enqueued'], 0)
        self.assertEqual(stats['total_dequeued'], 0)
        self.assertEqual(stats['current_size'], 0)
        
        # 执行操作
        self.queue.enqueue(self.job1)
        self.queue.enqueue(self.job2)
        self.queue.dequeue()
        
        # 检查统计
        stats = self.queue.get_stats()
        self.assertEqual(stats['total_enqueued'], 2)
        self.assertEqual(stats['total_dequeued'], 1)
        self.assertEqual(stats['current_size'], 1)


class TestQueueManager(unittest.TestCase):
    """测试队列管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.manager = QueueManager()
        self.job1 = Job(job_id="job-1", name="任务1", command="echo")
        self.job2 = Job(job_id="job-2", name="任务2", command="echo")
    
    def test_create_delete_queue(self):
        """测试创建和删除队列"""
        # 创建队列
        self.assertTrue(self.manager.create_queue("test-queue", QueueType.FIFO))
        self.assertIn("test-queue", self.manager.list_queues())
        
        # 重复创建应该失败
        self.assertFalse(self.manager.create_queue("test-queue", QueueType.FIFO))
        
        # 删除队列
        self.assertTrue(self.manager.delete_queue("test-queue"))
        self.assertNotIn("test-queue", self.manager.list_queues())
        
        # 删除不存在的队列应该失败
        self.assertFalse(self.manager.delete_queue("nonexistent"))
    
    def test_default_queue(self):
        """测试默认队列"""
        # 默认队列应该存在
        self.assertIn("default", self.manager.list_queues())
        
        # 不能删除默认队列
        self.assertFalse(self.manager.delete_queue("default"))
    
    def test_enqueue_dequeue_jobs(self):
        """测试任务入队出队"""
        # 入队到默认队列
        self.assertTrue(self.manager.enqueue_job(self.job1))
        self.assertTrue(self.manager.enqueue_job(self.job2, priority=10))
        
        # 从默认队列出队
        job = self.manager.dequeue_job()
        self.assertIsNotNone(job)
        
        # 创建新队列并入队
        self.manager.create_queue("test-queue", QueueType.FIFO)
        self.assertTrue(self.manager.enqueue_job(self.job1, queue_id="test-queue"))
        
        # 从指定队列出队
        job = self.manager.dequeue_job("test-queue")
        self.assertEqual(job.job_id, "job-1")
    
    def test_get_next_job(self):
        """测试获取下一个任务"""
        # 创建多个队列
        self.manager.create_queue("high-priority", QueueType.PRIORITY)
        self.manager.create_queue("low-priority", QueueType.PRIORITY)
        
        # 添加任务到不同队列
        self.manager.enqueue_job(self.job1, queue_id="low-priority")
        self.manager.enqueue_job(self.job2, queue_id="high-priority")
        
        # 按优先级获取任务
        job = self.manager.get_next_job(["high-priority", "low-priority"])
        self.assertEqual(job.job_id, "job-2")  # 来自高优先级队列
        
        job = self.manager.get_next_job(["high-priority", "low-priority"])
        self.assertEqual(job.job_id, "job-1")  # 来自低优先级队列
    
    def test_remove_job(self):
        """测试移除任务"""
        # 添加任务
        self.manager.enqueue_job(self.job1)
        self.manager.enqueue_job(self.job2, queue_id="default")
        
        # 移除任务
        self.assertTrue(self.manager.remove_job("job-1"))
        self.assertFalse(self.manager.remove_job("job-1"))  # 已移除
        
        # 从所有队列中查找并移除
        self.assertTrue(self.manager.remove_job("job-2"))
    
    def test_find_job(self):
        """测试查找任务"""
        # 添加任务到不同队列
        self.manager.create_queue("test-queue")
        self.manager.enqueue_job(self.job1, queue_id="default")
        self.manager.enqueue_job(self.job2, queue_id="test-queue")
        
        # 查找任务
        self.assertEqual(self.manager.find_job("job-1"), "default")
        self.assertEqual(self.manager.find_job("job-2"), "test-queue")
        self.assertIsNone(self.manager.find_job("nonexistent"))
    
    def test_stats(self):
        """测试统计信息"""
        # 添加任务
        self.manager.enqueue_job(self.job1)
        self.manager.enqueue_job(self.job2)
        
        # 获取统计
        stats = self.manager.get_all_stats()
        
        self.assertGreaterEqual(stats['total_queues'], 1)
        self.assertEqual(stats['total_pending_jobs'], 2)
        self.assertIn('queues', stats)
        self.assertIn('default', stats['queues'])


if __name__ == '__main__':
    unittest.main()
