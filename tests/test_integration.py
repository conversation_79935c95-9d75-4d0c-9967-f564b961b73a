"""
集群任务管理平台 - 集成测试
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.models import Job, Runner, JobStatus, RunnerStatus, ResourceLimits, ResourceUsage
from common.task_queue import QueueManager, QueueType
from common.scheduler import JobScheduler, SchedulingPolicy
from common.dependency_manager import DependencyManager
from common.retry_manager import RetryManager, FailureType
from common.monitoring import MetricsCollector, StatusMonitor
from common.alerting import AlertManager, AlertLevel, AlertType


class TestSystemIntegration(unittest.TestCase):
    """测试系统集成"""
    
    def setUp(self):
        """设置测试环境"""
        # 初始化各个组件
        self.queue_manager = QueueManager()
        self.scheduler = JobScheduler(SchedulingPolicy.LOAD_BALANCE)
        self.dependency_manager = DependencyManager()
        self.retry_manager = RetryManager()
        self.metrics_collector = MetricsCollector(collection_interval=1)
        self.status_monitor = StatusMonitor(self.metrics_collector)
        self.alert_manager = AlertManager()
        
        # 创建测试数据
        self.job1 = Job(
            job_id="job-1",
            name="任务1",
            command="echo",
            args=["hello"],
            priority=5
        )
        
        self.job2 = Job(
            job_id="job-2",
            name="任务2",
            command="echo",
            args=["world"],
            priority=10,
            dependencies=["job-1"]  # 依赖job-1
        )
        
        self.runner = Runner(
            runner_id="test-runner",
            name="测试Runner",
            host="localhost",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=ResourceLimits(80.0, 1024, 3600),
            resource_usage=ResourceUsage(20.0, 200, 100),
            capabilities=["python", "docker"]
        )
    
    def test_job_lifecycle(self):
        """测试完整的任务生命周期"""
        # 1. 任务入队
        self.assertTrue(self.queue_manager.enqueue_job(self.job1))
        self.assertEqual(self.queue_manager.get_total_pending_jobs(), 1)
        
        # 2. 任务调度
        available_runners = [self.runner]
        selected_runner = self.scheduler.schedule_job(self.job1, available_runners)
        self.assertIsNotNone(selected_runner)
        self.assertEqual(selected_runner.runner_id, "test-runner")
        
        # 3. 任务执行（模拟）
        self.scheduler.active_assignments[self.job1.job_id] = selected_runner.runner_id
        
        # 4. 任务完成
        self.scheduler.job_completed(self.job1.job_id)
        self.assertNotIn(self.job1.job_id, self.scheduler.active_assignments)
    
    def test_dependency_workflow(self):
        """测试依赖工作流"""
        # 1. 添加任务依赖
        self.dependency_manager.add_job_dependency(self.job2)
        
        # 2. 检查job2不能执行（依赖job1）
        can_execute, blocking = self.dependency_manager.can_execute_job("job-2")
        self.assertFalse(can_execute)
        self.assertIn("job-1", blocking)
        
        # 3. job1完成
        self.dependency_manager.update_job_status("job-1", JobStatus.COMPLETED)
        
        # 4. 现在job2可以执行
        can_execute, blocking = self.dependency_manager.can_execute_job("job-2")
        self.assertTrue(can_execute)
        self.assertEqual(len(blocking), 0)
    
    def test_retry_mechanism(self):
        """测试重试机制"""
        # 1. 注册可重试任务
        self.retry_manager.register_job(self.job1, max_retries=3)
        
        # 2. 模拟任务失败
        can_retry = self.retry_manager.handle_job_failure(
            "job-1", 
            "网络连接失败", 
            FailureType.NETWORK_ERROR,
            "test-runner"
        )
        self.assertTrue(can_retry)
        
        # 3. 检查重试信息
        retry_info = self.retry_manager.get_retry_info("job-1")
        self.assertIsNotNone(retry_info)
        self.assertEqual(retry_info['current_attempts'], 1)
        self.assertEqual(retry_info['max_retries'], 3)
        
        # 4. 模拟多次失败直到达到最大重试次数
        self.retry_manager.handle_job_failure("job-1", "再次失败", FailureType.EXECUTION_ERROR)
        self.retry_manager.handle_job_failure("job-1", "第三次失败", FailureType.EXECUTION_ERROR)
        
        # 5. 第四次失败应该放弃任务
        can_retry = self.retry_manager.handle_job_failure("job-1", "最后失败", FailureType.EXECUTION_ERROR)
        self.assertFalse(can_retry)
    
    def test_monitoring_and_alerting(self):
        """测试监控和告警"""
        # 1. 启动监控组件
        self.metrics_collector.start()
        self.status_monitor.start()
        self.alert_manager.start()
        
        try:
            # 2. 记录指标
            from common.monitoring import MetricType
            self.metrics_collector.record_metric(
                "test_cpu_usage", 
                95.0, 
                MetricType.GAUGE, 
                {"component": "test"}
            )
            
            # 3. 检查告警
            self.alert_manager.check_alerts({"cpu_percent": 95.0, "source": "test"})
            
            # 4. 验证告警被触发
            active_alerts = self.alert_manager.get_active_alerts()
            self.assertGreater(len(active_alerts), 0)
            
            # 5. 检查告警类型
            cpu_alerts = [a for a in active_alerts if a.alert_type == AlertType.SYSTEM_RESOURCE]
            self.assertGreater(len(cpu_alerts), 0)
            
        finally:
            # 清理
            self.metrics_collector.shutdown()
            self.status_monitor.shutdown()
            self.alert_manager.shutdown()
    
    def test_queue_and_scheduler_integration(self):
        """测试队列和调度器集成"""
        # 1. 创建多个优先级队列
        self.queue_manager.create_queue("high-priority", QueueType.PRIORITY)
        self.queue_manager.create_queue("low-priority", QueueType.PRIORITY)
        
        # 2. 添加不同优先级的任务
        high_job = Job(job_id="high-job", name="高优先级任务", command="echo", priority=10)
        low_job = Job(job_id="low-job", name="低优先级任务", command="echo", priority=1)
        
        self.queue_manager.enqueue_job(high_job, "high-priority", priority=10)
        self.queue_manager.enqueue_job(low_job, "low-priority", priority=1)
        
        # 3. 按优先级获取任务
        next_job = self.queue_manager.get_next_job(["high-priority", "low-priority"])
        self.assertEqual(next_job.job_id, "high-job")
        
        next_job = self.queue_manager.get_next_job(["high-priority", "low-priority"])
        self.assertEqual(next_job.job_id, "low-job")
    
    def test_complex_dependency_scenario(self):
        """测试复杂依赖场景"""
        # 创建复杂的依赖关系
        # job1 -> job2, job3
        # job2, job3 -> job4
        
        job3 = Job(job_id="job-3", name="任务3", command="echo", dependencies=["job-1"])
        job4 = Job(job_id="job-4", name="任务4", command="echo", dependencies=["job-2", "job-3"])
        
        # 添加依赖
        self.dependency_manager.add_job_dependency(self.job2)  # job2 depends on job1
        self.dependency_manager.add_job_dependency(job3)      # job3 depends on job1
        self.dependency_manager.add_job_dependency(job4)      # job4 depends on job2, job3
        
        # 获取执行计划
        execution_plan = self.dependency_manager.get_execution_plan(
            ["job-1", "job-2", "job-3", "job-4"]
        )
        
        # 验证执行计划
        self.assertEqual(len(execution_plan), 3)  # 3个执行层级
        self.assertIn("job-1", execution_plan[0])  # job1在第一层
        self.assertIn("job-2", execution_plan[1])  # job2在第二层
        self.assertIn("job-3", execution_plan[1])  # job3在第二层
        self.assertIn("job-4", execution_plan[2])  # job4在第三层
    
    def test_resource_monitoring_integration(self):
        """测试资源监控集成"""
        from common.monitoring import SystemMetrics, JobMetrics, RunnerMetrics
        
        # 1. 启动监控
        self.metrics_collector.start()
        
        try:
            # 2. 记录系统指标
            system_metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=75.0,
                memory_percent=60.0,
                memory_used_mb=600,
                memory_total_mb=1000,
                disk_percent=50.0,
                disk_used_gb=50,
                disk_total_gb=100,
                network_bytes_sent=1000000,
                network_bytes_recv=2000000,
                process_count=150,
                load_average=[1.5, 1.2, 1.0]
            )
            
            # 3. 记录任务指标
            job_metrics = JobMetrics(
                job_id="test-job",
                status="completed",
                start_time=time.time() - 100,
                end_time=time.time(),
                duration=100.0,
                runner_id="test-runner",
                cpu_usage=30.0,
                memory_usage=200,
                exit_code=0,
                timestamp=time.time()
            )
            
            # 4. 记录Runner指标
            runner_metrics = RunnerMetrics(
                runner_id="test-runner",
                status="active",
                cpu_percent=40.0,
                memory_mb=400,
                active_jobs=2,
                total_jobs_completed=10,
                total_jobs_failed=1,
                uptime=3600.0,
                last_heartbeat=time.time(),
                timestamp=time.time()
            )
            
            # 5. 记录指标
            self.metrics_collector.record_job_metrics(job_metrics)
            self.metrics_collector.record_runner_metrics(runner_metrics)
            
            # 6. 验证指标记录
            job_metrics_list = self.metrics_collector.get_job_metrics("test-job")
            self.assertGreater(len(job_metrics_list), 0)
            
            runner_metrics_list = self.metrics_collector.get_runner_metrics("test-runner")
            self.assertGreater(len(runner_metrics_list), 0)
            
        finally:
            self.metrics_collector.shutdown()
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 这是一个完整的端到端测试，模拟真实的任务处理流程
        
        # 1. 系统初始化
        components = [
            self.dependency_manager,
            self.retry_manager,
            self.alert_manager
        ]
        
        for component in components:
            component.start()
        
        try:
            # 2. 提交任务
            self.queue_manager.enqueue_job(self.job1, priority=5)
            self.queue_manager.enqueue_job(self.job2, priority=10)
            
            # 3. 处理依赖
            self.dependency_manager.add_job_dependency(self.job2)
            
            # 4. 获取可执行任务
            pending_jobs = ["job-1", "job-2"]
            ready_jobs = self.dependency_manager.get_ready_jobs(pending_jobs)
            self.assertEqual(ready_jobs, ["job-1"])  # 只有job1可以执行
            
            # 5. 调度任务
            available_runners = [self.runner]
            selected_runner = self.scheduler.schedule_job(self.job1, available_runners)
            self.assertIsNotNone(selected_runner)
            
            # 6. 模拟任务执行完成
            self.dependency_manager.update_job_status("job-1", JobStatus.COMPLETED)
            
            # 7. 检查job2现在可以执行
            ready_jobs = self.dependency_manager.get_ready_jobs(["job-2"])
            self.assertEqual(ready_jobs, ["job-2"])
            
            # 8. 调度job2
            selected_runner = self.scheduler.schedule_job(self.job2, available_runners)
            self.assertIsNotNone(selected_runner)
            
            # 9. 验证系统状态
            stats = self.scheduler.get_stats()
            self.assertEqual(stats['successful_schedules'], 2)
            
        finally:
            # 清理
            for component in components:
                component.shutdown()


if __name__ == '__main__':
    unittest.main()
