"""
集群任务管理平台 - 调度器测试
"""

import unittest
from unittest.mock import Mock, patch, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.scheduler import JobScheduler, SchedulingPolicy, SchedulingDecision
from common.load_balancer import LoadBalancerManager, LoadBalanceStrategy
from common.models import Job, Runner, JobStatus, RunnerStatus, ResourceLimits, ResourceUsage


class TestJobScheduler(unittest.TestCase):
    """测试任务调度器"""
    
    def setUp(self):
        """设置测试环境"""
        self.scheduler = JobScheduler(SchedulingPolicy.LOAD_BALANCE)
        
        # 创建测试任务
        self.job = Job(
            job_id="test-job",
            name="测试任务",
            command="echo",
            args=["hello"]
        )
        
        # 创建测试Runner
        self.runner1 = Runner(
            runner_id="runner-1",
            name="Runner 1",
            host="host1",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=ResourceLimits(80.0, 1024, 3600),
            resource_usage=ResourceUsage(20.0, 200, 100),
            capabilities=["python", "docker"]
        )
        
        self.runner2 = Runner(
            runner_id="runner-2",
            name="Runner 2",
            host="host2",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=ResourceLimits(80.0, 2048, 3600),
            resource_usage=ResourceUsage(50.0, 800, 200),
            capabilities=["python", "java"]
        )
        
        self.runner3 = Runner(
            runner_id="runner-3",
            name="Runner 3",
            host="host3",
            port=8080,
            status=RunnerStatus.BUSY,  # 忙碌状态
            resource_limits=ResourceLimits(80.0, 1024, 3600),
            resource_usage=ResourceUsage(10.0, 100, 50),
            capabilities=["python"]
        )
    
    def test_schedule_job_success(self):
        """测试成功调度任务"""
        available_runners = [self.runner1, self.runner2]
        
        selected_runner = self.scheduler.schedule_job(self.job, available_runners)
        
        # 应该选择一个Runner
        self.assertIsNotNone(selected_runner)
        self.assertIn(selected_runner, available_runners)
        
        # 检查调度记录
        self.assertIn(self.job.job_id, self.scheduler.active_assignments)
        self.assertEqual(
            self.scheduler.active_assignments[self.job.job_id],
            selected_runner.runner_id
        )
    
    def test_schedule_job_no_suitable_runners(self):
        """测试没有合适Runner的情况"""
        # 只提供忙碌的Runner
        available_runners = [self.runner3]
        
        selected_runner = self.scheduler.schedule_job(self.job, available_runners)
        
        # 应该返回None
        self.assertIsNone(selected_runner)
        
        # 不应该有调度记录
        self.assertNotIn(self.job.job_id, self.scheduler.active_assignments)
    
    def test_schedule_job_empty_runners(self):
        """测试空Runner列表"""
        available_runners = []
        
        selected_runner = self.scheduler.schedule_job(self.job, available_runners)
        
        # 应该返回None
        self.assertIsNone(selected_runner)
    
    def test_filter_suitable_runners(self):
        """测试过滤合适的Runner"""
        all_runners = [self.runner1, self.runner2, self.runner3]
        
        suitable_runners = self.scheduler._filter_suitable_runners(self.job, all_runners)
        
        # 应该过滤掉忙碌的Runner
        self.assertEqual(len(suitable_runners), 2)
        self.assertIn(self.runner1, suitable_runners)
        self.assertIn(self.runner2, suitable_runners)
        self.assertNotIn(self.runner3, suitable_runners)
    
    def test_capability_matching(self):
        """测试能力匹配"""
        # 设置任务需要特定能力
        self.job.required_capabilities = ["java"]
        
        all_runners = [self.runner1, self.runner2]  # runner1没有java能力
        
        suitable_runners = self.scheduler._filter_suitable_runners(self.job, all_runners)
        
        # 只有runner2有java能力
        self.assertEqual(len(suitable_runners), 1)
        self.assertEqual(suitable_runners[0], self.runner2)
    
    def test_resource_requirements(self):
        """测试资源要求检查"""
        # 设置高资源需求
        self.job.estimated_cpu_percent = 70.0
        self.job.estimated_memory_mb = 1000
        
        # runner1: CPU 20% + 70% = 90% > 80% (超限)
        # runner2: CPU 50% + 70% = 120% > 80% (超限)
        all_runners = [self.runner1, self.runner2]
        
        suitable_runners = self.scheduler._filter_suitable_runners(self.job, all_runners)
        
        # 应该没有合适的Runner
        self.assertEqual(len(suitable_runners), 0)
    
    def test_different_scheduling_policies(self):
        """测试不同调度策略"""
        available_runners = [self.runner1, self.runner2]
        
        # 测试首次适应
        scheduler_ff = JobScheduler(SchedulingPolicy.FIRST_FIT)
        selected = scheduler_ff._select_runner(self.job, available_runners)
        self.assertEqual(selected, self.runner1)  # 第一个
        
        # 测试负载均衡
        scheduler_lb = JobScheduler(SchedulingPolicy.LOAD_BALANCE)
        selected = scheduler_lb._select_runner(self.job, available_runners)
        self.assertEqual(selected, self.runner1)  # CPU使用率更低
    
    def test_job_completed(self):
        """测试任务完成处理"""
        # 先调度任务
        available_runners = [self.runner1]
        self.scheduler.schedule_job(self.job, available_runners)
        
        # 检查任务已分配
        self.assertIn(self.job.job_id, self.scheduler.active_assignments)
        self.assertEqual(self.scheduler.runner_loads["runner-1"], 1)
        
        # 任务完成
        self.scheduler.job_completed(self.job.job_id)
        
        # 检查任务已移除
        self.assertNotIn(self.job.job_id, self.scheduler.active_assignments)
        self.assertEqual(self.scheduler.runner_loads["runner-1"], 0)
    
    def test_scheduling_callbacks(self):
        """测试调度回调"""
        callback_called = False
        callback_job = None
        callback_runner = None
        
        def test_callback(job, runner, decision):
            nonlocal callback_called, callback_job, callback_runner
            callback_called = True
            callback_job = job
            callback_runner = runner
        
        # 添加回调
        self.scheduler.add_scheduling_callback(test_callback)
        
        # 调度任务
        available_runners = [self.runner1]
        selected_runner = self.scheduler.schedule_job(self.job, available_runners)
        
        # 检查回调是否被调用
        self.assertTrue(callback_called)
        self.assertEqual(callback_job, self.job)
        self.assertEqual(callback_runner, selected_runner)
    
    def test_scheduling_stats(self):
        """测试调度统计"""
        # 初始统计
        stats = self.scheduler.get_stats()
        self.assertEqual(stats['total_scheduled'], 0)
        self.assertEqual(stats['successful_schedules'], 0)
        self.assertEqual(stats['failed_schedules'], 0)
        
        # 成功调度
        available_runners = [self.runner1]
        self.scheduler.schedule_job(self.job, available_runners)
        
        stats = self.scheduler.get_stats()
        self.assertEqual(stats['total_scheduled'], 1)
        self.assertEqual(stats['successful_schedules'], 1)
        self.assertEqual(stats['failed_schedules'], 0)
        
        # 失败调度
        self.scheduler.schedule_job(self.job, [])  # 空Runner列表
        
        stats = self.scheduler.get_stats()
        self.assertEqual(stats['total_scheduled'], 2)
        self.assertEqual(stats['successful_schedules'], 1)
        self.assertEqual(stats['failed_schedules'], 1)


class TestLoadBalancerIntegration(unittest.TestCase):
    """测试负载均衡器集成"""
    
    def setUp(self):
        """设置测试环境"""
        self.load_balancer = LoadBalancerManager(LoadBalanceStrategy.RESOURCE_BASED)
        
        # 创建测试任务和Runner
        self.job = Job(job_id="test-job", name="测试任务", command="echo")
        
        self.runner1 = Runner(
            runner_id="runner-1",
            name="Runner 1",
            host="host1",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=ResourceLimits(80.0, 1024, 3600),
            resource_usage=ResourceUsage(20.0, 200, 100),
            capabilities=["python"]
        )
        
        self.runner2 = Runner(
            runner_id="runner-2",
            name="Runner 2",
            host="host2",
            port=8080,
            status=RunnerStatus.IDLE,
            resource_limits=ResourceLimits(80.0, 2048, 3600),
            resource_usage=ResourceUsage(60.0, 1000, 200),
            capabilities=["python"]
        )
    
    def test_resource_based_selection(self):
        """测试基于资源的选择"""
        # 更新Runner信息
        self.load_balancer.update_runner(self.runner1)
        self.load_balancer.update_runner(self.runner2)
        
        available_runners = [self.runner1, self.runner2]
        
        # 选择Runner
        selected = self.load_balancer.select_runner(self.job, available_runners)
        
        # 应该选择资源使用率更低的runner1
        self.assertEqual(selected, self.runner1)
    
    def test_round_robin_selection(self):
        """测试轮询选择"""
        self.load_balancer.set_strategy(LoadBalanceStrategy.ROUND_ROBIN)
        
        available_runners = [self.runner1, self.runner2]
        
        # 连续选择应该轮询
        selected1 = self.load_balancer.select_runner(self.job, available_runners)
        selected2 = self.load_balancer.select_runner(self.job, available_runners)
        
        # 应该选择不同的Runner
        self.assertNotEqual(selected1, selected2)
    
    def test_load_metrics_update(self):
        """测试负载指标更新"""
        self.load_balancer.update_runner(self.runner1)
        
        # 更新任务指标
        self.load_balancer.update_job_metrics("runner-1", job_completed=True, response_time=5.0)
        
        # 获取负载指标
        metrics = self.load_balancer.get_load_metrics()
        
        self.assertIn("runner-1", metrics)
        runner_metrics = metrics["runner-1"]
        self.assertEqual(runner_metrics.total_jobs, 1)
        self.assertEqual(runner_metrics.avg_response_time, 5.0)
    
    def test_strategy_switching(self):
        """测试策略切换"""
        # 初始策略
        self.assertEqual(
            self.load_balancer.get_current_strategy(),
            LoadBalanceStrategy.RESOURCE_BASED
        )
        
        # 切换策略
        self.load_balancer.set_strategy(LoadBalanceStrategy.RANDOM)
        self.assertEqual(
            self.load_balancer.get_current_strategy(),
            LoadBalanceStrategy.RANDOM
        )
    
    def test_load_balancer_stats(self):
        """测试负载均衡器统计"""
        available_runners = [self.runner1, self.runner2]
        
        # 执行选择
        self.load_balancer.select_runner(self.job, available_runners)
        self.load_balancer.select_runner(self.job, available_runners)
        
        # 获取统计
        stats = self.load_balancer.get_stats()
        
        self.assertEqual(stats['total_selections'], 2)
        self.assertGreater(stats['avg_selection_time'], 0)


if __name__ == '__main__':
    unittest.main()
