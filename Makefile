# 集群任务管理平台 Makefile

.PHONY: help install test clean run-manager run-runner dev-setup

help:
	@echo "可用命令:"
	@echo "  install     - 安装项目依赖"
	@echo "  test        - 运行测试"
	@echo "  clean       - 清理临时文件"
	@echo "  run-manager - 启动管理程序"
	@echo "  run-runner  - 启动Runner"
	@echo "  dev-setup   - 开发环境设置"

install:
	pip install -e .
	pip install -r requirements.txt

test:
	python -m pytest tests/ -v

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/

run-manager:
	python -m cluster_manager.main

run-runner:
	python -m runner.main

dev-setup:
	pip install -e .
	pip install pytest
	mkdir -p logs data
	@echo "开发环境设置完成"
