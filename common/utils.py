"""
集群任务管理平台 - 通用工具函数模块
"""

import os
import sys
import time
import uuid
import hashlib
import socket
import psutil
import threading
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timedelta
from pathlib import Path


def generate_id(prefix: str = "") -> str:
    """生成唯一ID"""
    unique_id = str(uuid.uuid4())
    return f"{prefix}-{unique_id}" if prefix else unique_id


def get_timestamp() -> float:
    """获取当前时间戳"""
    return time.time()


def format_timestamp(timestamp: float, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间戳"""
    return datetime.fromtimestamp(timestamp).strftime(format_str)


def get_hostname() -> str:
    """获取主机名"""
    return socket.gethostname()


def get_local_ip() -> str:
    """获取本地IP地址"""
    try:
        # 连接到一个远程地址来获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"


def check_port_available(host: str, port: int) -> bool:
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0
    except Exception:
        return False


def find_available_port(start_port: int = 8080, max_attempts: int = 100) -> int:
    """查找可用端口"""
    for i in range(max_attempts):
        port = start_port + i
        if check_port_available("localhost", port):
            return port
    raise RuntimeError(f"无法找到可用端口，起始端口: {start_port}")


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    return {
        "hostname": get_hostname(),
        "platform": sys.platform,
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "disk_usage": psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total
    }


def get_process_info(pid: Optional[int] = None) -> Dict[str, Any]:
    """获取进程信息"""
    try:
        process = psutil.Process(pid) if pid else psutil.Process()
        return {
            "pid": process.pid,
            "name": process.name(),
            "cpu_percent": process.cpu_percent(),
            "memory_info": process.memory_info()._asdict(),
            "create_time": process.create_time(),
            "status": process.status()
        }
    except psutil.NoSuchProcess:
        return {}


def calculate_hash(data: str, algorithm: str = "md5") -> str:
    """计算数据哈希值"""
    if algorithm.lower() == "md5":
        return hashlib.md5(data.encode()).hexdigest()
    elif algorithm.lower() == "sha256":
        return hashlib.sha256(data.encode()).hexdigest()
    else:
        raise ValueError(f"不支持的哈希算法: {algorithm}")


def ensure_directory(path: str):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)


def safe_remove_file(filepath: str) -> bool:
    """安全删除文件"""
    try:
        if os.path.exists(filepath):
            os.remove(filepath)
            return True
        return False
    except Exception:
        return False


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      backoff: float = 2.0, exceptions: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            retries = 0
            current_delay = delay
            
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retries += 1
                    if retries >= max_retries:
                        raise e
                    
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def timeout_handler(timeout_seconds: int):
    """超时装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            result = [None]
            exception = [None]
            
            def target():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()
            thread.join(timeout_seconds)
            
            if thread.is_alive():
                raise TimeoutError(f"函数执行超时: {timeout_seconds}秒")
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        return wrapper
    return decorator


class Timer:
    """计时器工具类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
        return self
    
    def elapsed(self) -> float:
        """获取经过的时间（秒）"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time if self.end_time else time.time()
        return end - self.start_time
    
    def __enter__(self):
        return self.start()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def can_proceed(self) -> bool:
        """检查是否可以继续执行"""
        with self.lock:
            now = time.time()
            # 清理过期的调用记录
            self.calls = [call_time for call_time in self.calls 
                         if now - call_time < self.time_window]
            
            if len(self.calls) < self.max_calls:
                self.calls.append(now)
                return True
            return False
    
    def wait_if_needed(self):
        """如果需要则等待"""
        while not self.can_proceed():
            time.sleep(0.1)


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        with self.lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.timeout:
                    self.state = "HALF_OPEN"
                else:
                    raise Exception("熔断器开启，拒绝调用")
            
            try:
                result = func(*args, **kwargs)
                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                return result
            
            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                
                raise e


def parse_size_string(size_str: str) -> int:
    """解析大小字符串，如 '10MB' -> 字节数"""
    size_str = size_str.upper().strip()
    
    if size_str.endswith('B'):
        size_str = size_str[:-1]
    
    if size_str.endswith('K'):
        return int(size_str[:-1]) * 1024
    elif size_str.endswith('M'):
        return int(size_str[:-1]) * 1024 * 1024
    elif size_str.endswith('G'):
        return int(size_str[:-1]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def format_size(size_bytes: int) -> str:
    """格式化字节大小为可读字符串"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f}{unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f}PB"


# 示例使用
if __name__ == "__main__":
    # 测试计时器
    with Timer() as timer:
        time.sleep(1)
    print(f"执行时间: {timer.elapsed():.2f}秒")
    
    # 测试重试装饰器
    @retry_on_exception(max_retries=3, delay=0.1)
    def unreliable_function():
        import random
        if random.random() < 0.7:
            raise Exception("随机失败")
        return "成功"
    
    try:
        result = unreliable_function()
        print(f"重试结果: {result}")
    except Exception as e:
        print(f"重试失败: {e}")
    
    # 测试系统信息
    info = get_system_info()
    print(f"系统信息: {info}")
