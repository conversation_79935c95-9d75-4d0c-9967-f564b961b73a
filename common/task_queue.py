"""
集群任务管理平台 - 任务队列管理
"""

import threading
import heapq
import time
from typing import Dict, List, Optional, Set, Callable, Any
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum

from .logger import LoggerMixin
from .models import Job, JobStatus
from .utils import get_timestamp, generate_id


class QueueType(Enum):
    """队列类型"""
    PRIORITY = "priority"      # 优先级队列
    FIFO = "fifo"             # 先进先出队列
    LIFO = "lifo"             # 后进先出队列
    ROUND_ROBIN = "round_robin"  # 轮询队列


@dataclass
class QueuedJob:
    """队列中的任务"""
    job: Job
    priority: int = 0
    queue_time: float = field(default_factory=get_timestamp)
    attempts: int = 0
    
    def __lt__(self, other):
        # 优先级高的排在前面（数字越大优先级越高）
        if self.priority != other.priority:
            return self.priority > other.priority
        # 优先级相同时，先入队的排在前面
        return self.queue_time < other.queue_time


class TaskQueue(LoggerMixin):
    """任务队列"""
    
    def __init__(self, queue_id: str, queue_type: QueueType = QueueType.PRIORITY,
                 max_size: int = 1000):
        self.queue_id = queue_id
        self.queue_type = queue_type
        self.max_size = max_size
        
        # 队列存储
        if queue_type == QueueType.PRIORITY:
            self._queue = []  # 使用堆
        else:
            self._queue = deque()
        
        # 任务索引
        self.job_index: Dict[str, QueuedJob] = {}
        
        # 统计信息
        self.stats = {
            'total_enqueued': 0,
            'total_dequeued': 0,
            'current_size': 0,
            'max_size_reached': 0
        }
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 回调函数
        self.enqueue_callbacks: List[Callable] = []
        self.dequeue_callbacks: List[Callable] = []
        
        self.logger.info(f"任务队列初始化: {queue_id} ({queue_type.value})")
    
    def enqueue(self, job: Job, priority: int = 0) -> bool:
        """入队任务"""
        with self.lock:
            # 检查队列大小限制
            if len(self._queue) >= self.max_size:
                self.logger.warning(f"队列已满，无法添加任务: {job.job_id}")
                return False
            
            # 检查任务是否已存在
            if job.job_id in self.job_index:
                self.logger.warning(f"任务已在队列中: {job.job_id}")
                return False
            
            # 创建队列任务
            queued_job = QueuedJob(job=job, priority=priority)
            
            # 根据队列类型添加任务
            if self.queue_type == QueueType.PRIORITY:
                heapq.heappush(self._queue, queued_job)
            elif self.queue_type == QueueType.FIFO:
                self._queue.append(queued_job)
            elif self.queue_type == QueueType.LIFO:
                self._queue.append(queued_job)
            
            # 更新索引
            self.job_index[job.job_id] = queued_job
            
            # 更新统计
            self.stats['total_enqueued'] += 1
            self.stats['current_size'] = len(self._queue)
            self.stats['max_size_reached'] = max(
                self.stats['max_size_reached'], 
                self.stats['current_size']
            )
            
            self.logger.debug(f"任务入队: {job.job_id} (优先级: {priority})")
            
            # 调用回调
            for callback in self.enqueue_callbacks:
                try:
                    callback(job, priority)
                except Exception as e:
                    self.logger.error(f"入队回调异常: {e}")
            
            return True
    
    def dequeue(self) -> Optional[Job]:
        """出队任务"""
        with self.lock:
            if not self._queue:
                return None
            
            # 根据队列类型获取任务
            if self.queue_type == QueueType.PRIORITY:
                queued_job = heapq.heappop(self._queue)
            elif self.queue_type == QueueType.FIFO:
                queued_job = self._queue.popleft()
            elif self.queue_type == QueueType.LIFO:
                queued_job = self._queue.pop()
            else:
                queued_job = self._queue.popleft()
            
            # 更新索引
            del self.job_index[queued_job.job.job_id]
            
            # 更新统计
            self.stats['total_dequeued'] += 1
            self.stats['current_size'] = len(self._queue)
            
            self.logger.debug(f"任务出队: {queued_job.job.job_id}")
            
            # 调用回调
            for callback in self.dequeue_callbacks:
                try:
                    callback(queued_job.job)
                except Exception as e:
                    self.logger.error(f"出队回调异常: {e}")
            
            return queued_job.job
    
    def peek(self) -> Optional[Job]:
        """查看队首任务但不出队"""
        with self.lock:
            if not self._queue:
                return None
            
            if self.queue_type == QueueType.PRIORITY:
                return self._queue[0].job
            elif self.queue_type == QueueType.FIFO:
                return self._queue[0].job
            elif self.queue_type == QueueType.LIFO:
                return self._queue[-1].job
            else:
                return self._queue[0].job
    
    def remove(self, job_id: str) -> bool:
        """移除指定任务"""
        with self.lock:
            if job_id not in self.job_index:
                return False
            
            queued_job = self.job_index[job_id]
            
            # 从队列中移除
            if self.queue_type == QueueType.PRIORITY:
                # 对于堆，标记为已移除，在出队时跳过
                queued_job.job = None
            else:
                try:
                    self._queue.remove(queued_job)
                except ValueError:
                    pass
            
            # 更新索引
            del self.job_index[job_id]
            
            # 更新统计
            self.stats['current_size'] = len(self.job_index)
            
            self.logger.debug(f"任务移除: {job_id}")
            return True
    
    def contains(self, job_id: str) -> bool:
        """检查任务是否在队列中"""
        with self.lock:
            return job_id in self.job_index
    
    def size(self) -> int:
        """获取队列大小"""
        with self.lock:
            return len(self.job_index)
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self.size() == 0
    
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return self.size() >= self.max_size
    
    def clear(self):
        """清空队列"""
        with self.lock:
            self._queue.clear()
            self.job_index.clear()
            self.stats['current_size'] = 0
            self.logger.info(f"队列已清空: {self.queue_id}")
    
    def get_jobs(self) -> List[Job]:
        """获取队列中的所有任务"""
        with self.lock:
            return [queued_job.job for queued_job in self.job_index.values() 
                   if queued_job.job is not None]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        with self.lock:
            return {
                'queue_id': self.queue_id,
                'queue_type': self.queue_type.value,
                'max_size': self.max_size,
                **self.stats
            }
    
    def add_enqueue_callback(self, callback: Callable):
        """添加入队回调"""
        self.enqueue_callbacks.append(callback)
    
    def add_dequeue_callback(self, callback: Callable):
        """添加出队回调"""
        self.dequeue_callbacks.append(callback)


class QueueManager(LoggerMixin):
    """队列管理器"""
    
    def __init__(self):
        self.queues: Dict[str, TaskQueue] = {}
        self.default_queue_id = "default"
        self.lock = threading.RLock()
        
        # 创建默认队列
        self.create_queue(self.default_queue_id, QueueType.PRIORITY)
        
        self.logger.info("队列管理器初始化完成")
    
    def create_queue(self, queue_id: str, queue_type: QueueType = QueueType.PRIORITY,
                    max_size: int = 1000) -> bool:
        """创建队列"""
        with self.lock:
            if queue_id in self.queues:
                self.logger.warning(f"队列已存在: {queue_id}")
                return False
            
            queue = TaskQueue(queue_id, queue_type, max_size)
            self.queues[queue_id] = queue
            
            self.logger.info(f"队列创建成功: {queue_id}")
            return True
    
    def delete_queue(self, queue_id: str) -> bool:
        """删除队列"""
        with self.lock:
            if queue_id == self.default_queue_id:
                self.logger.error("不能删除默认队列")
                return False
            
            if queue_id not in self.queues:
                self.logger.warning(f"队列不存在: {queue_id}")
                return False
            
            queue = self.queues[queue_id]
            if not queue.is_empty():
                self.logger.warning(f"队列不为空，无法删除: {queue_id}")
                return False
            
            del self.queues[queue_id]
            self.logger.info(f"队列删除成功: {queue_id}")
            return True
    
    def get_queue(self, queue_id: str) -> Optional[TaskQueue]:
        """获取队列"""
        with self.lock:
            return self.queues.get(queue_id)
    
    def list_queues(self) -> List[str]:
        """列出所有队列"""
        with self.lock:
            return list(self.queues.keys())
    
    def enqueue_job(self, job: Job, queue_id: str = None, priority: int = 0) -> bool:
        """将任务加入队列"""
        queue_id = queue_id or self.default_queue_id
        
        with self.lock:
            queue = self.queues.get(queue_id)
            if not queue:
                self.logger.error(f"队列不存在: {queue_id}")
                return False
            
            return queue.enqueue(job, priority)
    
    def dequeue_job(self, queue_id: str = None) -> Optional[Job]:
        """从队列中取出任务"""
        queue_id = queue_id or self.default_queue_id
        
        with self.lock:
            queue = self.queues.get(queue_id)
            if not queue:
                self.logger.error(f"队列不存在: {queue_id}")
                return None
            
            return queue.dequeue()
    
    def get_next_job(self, queue_priorities: List[str] = None) -> Optional[Job]:
        """按优先级从多个队列中获取下一个任务"""
        queue_priorities = queue_priorities or [self.default_queue_id]
        
        with self.lock:
            for queue_id in queue_priorities:
                queue = self.queues.get(queue_id)
                if queue and not queue.is_empty():
                    job = queue.dequeue()
                    if job:
                        return job
            
            return None
    
    def remove_job(self, job_id: str, queue_id: str = None) -> bool:
        """从队列中移除任务"""
        with self.lock:
            if queue_id:
                # 从指定队列移除
                queue = self.queues.get(queue_id)
                return queue.remove(job_id) if queue else False
            else:
                # 从所有队列中查找并移除
                for queue in self.queues.values():
                    if queue.remove(job_id):
                        return True
                return False
    
    def find_job(self, job_id: str) -> Optional[str]:
        """查找任务所在的队列"""
        with self.lock:
            for queue_id, queue in self.queues.items():
                if queue.contains(job_id):
                    return queue_id
            return None
    
    def get_total_pending_jobs(self) -> int:
        """获取所有队列中待处理任务总数"""
        with self.lock:
            return sum(queue.size() for queue in self.queues.values())
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有队列的统计信息"""
        with self.lock:
            return {
                'total_queues': len(self.queues),
                'total_pending_jobs': self.get_total_pending_jobs(),
                'queues': {
                    queue_id: queue.get_stats() 
                    for queue_id, queue in self.queues.items()
                }
            }


# 全局队列管理器实例
queue_manager = QueueManager()
