"""
集群任务管理平台 - 智能调度器
"""

import threading
import time
import random
from typing import Dict, List, Optional, Set, Callable, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict

from .logger import LoggerMixin
from .models import Job, Runner, JobStatus, RunnerStatus
from .task_queue import Queue<PERSON>ana<PERSON>, queue_manager
from .utils import get_timestamp


class SchedulingPolicy(Enum):
    """调度策略"""
    FIRST_FIT = "first_fit"           # 首次适应
    BEST_FIT = "best_fit"             # 最佳适应
    WORST_FIT = "worst_fit"           # 最差适应
    ROUND_ROBIN = "round_robin"       # 轮询
    LOAD_BALANCE = "load_balance"     # 负载均衡
    CAPABILITY_MATCH = "capability_match"  # 能力匹配


@dataclass
class SchedulingDecision:
    """调度决策"""
    job: Job
    runner: Runner
    score: float
    reason: str
    timestamp: float


class JobScheduler(LoggerMixin):
    """任务调度器"""
    
    def __init__(self, policy: SchedulingPolicy = SchedulingPolicy.LOAD_BALANCE):
        self.policy = policy
        self.running = False
        
        # 调度状态
        self.active_assignments: Dict[str, str] = {}  # job_id -> runner_id
        self.runner_loads: Dict[str, int] = defaultdict(int)  # runner_id -> job_count
        self.last_assigned_runner = 0  # 用于轮询调度
        
        # 调度历史
        self.scheduling_history: List[SchedulingDecision] = []
        self.max_history_size = 1000
        
        # 调度统计
        self.stats = {
            'total_scheduled': 0,
            'successful_schedules': 0,
            'failed_schedules': 0,
            'average_scheduling_time': 0.0
        }
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 回调函数
        self.scheduling_callbacks: List[Callable] = []
        
        self.logger.info(f"任务调度器初始化，策略: {policy.value}")
    
    def schedule_job(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        """调度单个任务"""
        start_time = time.time()
        
        try:
            with self.lock:
                # 过滤可用的Runner
                suitable_runners = self._filter_suitable_runners(job, available_runners)
                
                if not suitable_runners:
                    self.logger.warning(f"没有合适的Runner执行任务: {job.job_id}")
                    self.stats['failed_schedules'] += 1
                    return None
                
                # 根据策略选择Runner
                selected_runner = self._select_runner(job, suitable_runners)
                
                if selected_runner:
                    # 记录调度决策
                    decision = SchedulingDecision(
                        job=job,
                        runner=selected_runner,
                        score=self._calculate_score(job, selected_runner),
                        reason=f"Selected by {self.policy.value} policy",
                        timestamp=get_timestamp()
                    )
                    
                    self._record_decision(decision)
                    
                    # 更新状态
                    self.active_assignments[job.job_id] = selected_runner.runner_id
                    self.runner_loads[selected_runner.runner_id] += 1
                    
                    # 更新统计
                    self.stats['total_scheduled'] += 1
                    self.stats['successful_schedules'] += 1
                    
                    scheduling_time = time.time() - start_time
                    self._update_average_time(scheduling_time)
                    
                    self.logger.info(f"任务调度成功: {job.job_id} -> {selected_runner.runner_id}")
                    
                    # 调用回调
                    for callback in self.scheduling_callbacks:
                        try:
                            callback(job, selected_runner, decision)
                        except Exception as e:
                            self.logger.error(f"调度回调异常: {e}")
                    
                    return selected_runner
                else:
                    self.stats['failed_schedules'] += 1
                    return None
                    
        except Exception as e:
            self.logger.error(f"任务调度异常: {e}")
            self.stats['failed_schedules'] += 1
            return None
    
    def _filter_suitable_runners(self, job: Job, runners: List[Runner]) -> List[Runner]:
        """过滤合适的Runner"""
        suitable_runners = []
        
        for runner in runners:
            # 检查Runner状态
            if runner.status != RunnerStatus.IDLE:
                continue
            
            # 检查能力匹配
            if hasattr(job, 'required_capabilities') and job.required_capabilities:
                if not set(job.required_capabilities).issubset(set(runner.capabilities)):
                    continue
            
            # 检查资源要求
            if not self._check_resource_requirements(job, runner):
                continue
            
            suitable_runners.append(runner)
        
        return suitable_runners
    
    def _check_resource_requirements(self, job: Job, runner: Runner) -> bool:
        """检查资源要求"""
        # 检查CPU要求
        estimated_cpu = getattr(job, 'estimated_cpu_percent', 10.0)
        if runner.resource_usage.cpu_percent + estimated_cpu > runner.resource_limits.max_cpu_percent:
            return False
        
        # 检查内存要求
        estimated_memory = getattr(job, 'estimated_memory_mb', 100)
        if runner.resource_usage.memory_mb + estimated_memory > runner.resource_limits.max_memory_mb:
            return False
        
        return True
    
    def _select_runner(self, job: Job, runners: List[Runner]) -> Optional[Runner]:
        """根据策略选择Runner"""
        if not runners:
            return None
        
        if self.policy == SchedulingPolicy.FIRST_FIT:
            return runners[0]
        
        elif self.policy == SchedulingPolicy.BEST_FIT:
            return self._best_fit_selection(job, runners)
        
        elif self.policy == SchedulingPolicy.WORST_FIT:
            return self._worst_fit_selection(job, runners)
        
        elif self.policy == SchedulingPolicy.ROUND_ROBIN:
            return self._round_robin_selection(runners)
        
        elif self.policy == SchedulingPolicy.LOAD_BALANCE:
            return self._load_balance_selection(runners)
        
        elif self.policy == SchedulingPolicy.CAPABILITY_MATCH:
            return self._capability_match_selection(job, runners)
        
        else:
            return runners[0]
    
    def _best_fit_selection(self, job: Job, runners: List[Runner]) -> Runner:
        """最佳适应选择"""
        best_runner = None
        best_score = float('inf')
        
        for runner in runners:
            score = self._calculate_fit_score(job, runner)
            if score < best_score:
                best_score = score
                best_runner = runner
        
        return best_runner
    
    def _worst_fit_selection(self, job: Job, runners: List[Runner]) -> Runner:
        """最差适应选择"""
        worst_runner = None
        worst_score = -1
        
        for runner in runners:
            score = self._calculate_fit_score(job, runner)
            if score > worst_score:
                worst_score = score
                worst_runner = runner
        
        return worst_runner
    
    def _round_robin_selection(self, runners: List[Runner]) -> Runner:
        """轮询选择"""
        if not runners:
            return None
        
        self.last_assigned_runner = (self.last_assigned_runner + 1) % len(runners)
        return runners[self.last_assigned_runner]
    
    def _load_balance_selection(self, runners: List[Runner]) -> Runner:
        """负载均衡选择"""
        # 选择当前负载最低的Runner
        min_load = float('inf')
        selected_runner = None
        
        for runner in runners:
            current_load = self.runner_loads.get(runner.runner_id, 0)
            # 结合CPU使用率
            total_load = current_load + runner.resource_usage.cpu_percent / 100.0
            
            if total_load < min_load:
                min_load = total_load
                selected_runner = runner
        
        return selected_runner
    
    def _capability_match_selection(self, job: Job, runners: List[Runner]) -> Runner:
        """能力匹配选择"""
        required_capabilities = getattr(job, 'required_capabilities', [])
        
        if not required_capabilities:
            # 如果没有特殊要求，使用负载均衡
            return self._load_balance_selection(runners)
        
        # 计算能力匹配度
        best_runner = None
        best_match_score = -1
        
        for runner in runners:
            match_score = len(set(required_capabilities) & set(runner.capabilities))
            if match_score > best_match_score:
                best_match_score = match_score
                best_runner = runner
        
        return best_runner
    
    def _calculate_fit_score(self, job: Job, runner: Runner) -> float:
        """计算适应度分数"""
        # 基于资源使用率计算分数
        cpu_usage = runner.resource_usage.cpu_percent / 100.0
        memory_usage = runner.resource_usage.memory_mb / runner.resource_limits.max_memory_mb
        
        # 综合分数（越小越好）
        score = cpu_usage * 0.6 + memory_usage * 0.4
        return score
    
    def _calculate_score(self, job: Job, runner: Runner) -> float:
        """计算调度分数"""
        return self._calculate_fit_score(job, runner)
    
    def _record_decision(self, decision: SchedulingDecision):
        """记录调度决策"""
        self.scheduling_history.append(decision)
        
        # 限制历史记录大小
        if len(self.scheduling_history) > self.max_history_size:
            self.scheduling_history = self.scheduling_history[-self.max_history_size:]
    
    def _update_average_time(self, scheduling_time: float):
        """更新平均调度时间"""
        current_avg = self.stats['average_scheduling_time']
        total_scheduled = self.stats['total_scheduled']
        
        if total_scheduled == 1:
            self.stats['average_scheduling_time'] = scheduling_time
        else:
            # 计算移动平均
            self.stats['average_scheduling_time'] = (
                (current_avg * (total_scheduled - 1) + scheduling_time) / total_scheduled
            )
    
    def job_completed(self, job_id: str):
        """任务完成通知"""
        with self.lock:
            if job_id in self.active_assignments:
                runner_id = self.active_assignments[job_id]
                del self.active_assignments[job_id]
                
                if runner_id in self.runner_loads:
                    self.runner_loads[runner_id] = max(0, self.runner_loads[runner_id] - 1)
                
                self.logger.debug(f"任务完成，释放资源: {job_id} -> {runner_id}")
    
    def get_assignment(self, job_id: str) -> Optional[str]:
        """获取任务分配的Runner"""
        with self.lock:
            return self.active_assignments.get(job_id)
    
    def get_runner_load(self, runner_id: str) -> int:
        """获取Runner负载"""
        with self.lock:
            return self.runner_loads.get(runner_id, 0)
    
    def get_scheduling_history(self, limit: int = 100) -> List[SchedulingDecision]:
        """获取调度历史"""
        with self.lock:
            return self.scheduling_history[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取调度统计"""
        with self.lock:
            return {
                'policy': self.policy.value,
                'active_assignments': len(self.active_assignments),
                'runner_loads': dict(self.runner_loads),
                **self.stats
            }
    
    def add_scheduling_callback(self, callback: Callable):
        """添加调度回调"""
        self.scheduling_callbacks.append(callback)
    
    def set_policy(self, policy: SchedulingPolicy):
        """设置调度策略"""
        with self.lock:
            old_policy = self.policy
            self.policy = policy
            self.logger.info(f"调度策略变更: {old_policy.value} -> {policy.value}")


class BatchScheduler(LoggerMixin):
    """批量调度器"""
    
    def __init__(self, job_scheduler: JobScheduler, queue_manager: QueueManager):
        self.job_scheduler = job_scheduler
        self.queue_manager = queue_manager
        self.running = False
        
        # 调度配置
        self.batch_size = 10
        self.scheduling_interval = 5  # 秒
        
        # 调度线程
        self.scheduler_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info("批量调度器初始化完成")
    
    def start(self):
        """启动批量调度器"""
        if self.running:
            return
        
        self.logger.info("启动批量调度器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(
            target=self._scheduling_loop,
            name="BatchScheduler",
            daemon=True
        )
        self.scheduler_thread.start()
        
        self.logger.info("批量调度器启动成功")
    
    def shutdown(self):
        """关闭批量调度器"""
        if not self.running:
            return
        
        self.logger.info("关闭批量调度器...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=10)
        
        self.logger.info("批量调度器已关闭")
    
    def _scheduling_loop(self):
        """调度循环"""
        self.logger.info("批量调度循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 执行一轮调度
                scheduled_count = self._schedule_batch()
                
                if scheduled_count > 0:
                    self.logger.info(f"本轮调度了 {scheduled_count} 个任务")
                
                # 等待下一轮调度
                self.shutdown_event.wait(self.scheduling_interval)
                
            except Exception as e:
                self.logger.error(f"批量调度循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _schedule_batch(self) -> int:
        """执行一批调度"""
        scheduled_count = 0
        
        try:
            # 获取可用的Runner（这里需要从Runner管理器获取）
            available_runners = self._get_available_runners()
            
            if not available_runners:
                return 0
            
            # 从队列中获取待调度的任务
            for _ in range(min(self.batch_size, len(available_runners))):
                job = self.queue_manager.get_next_job()
                if not job:
                    break
                
                # 调度任务
                selected_runner = self.job_scheduler.schedule_job(job, available_runners)
                
                if selected_runner:
                    scheduled_count += 1
                    # 从可用Runner列表中移除已分配的Runner
                    available_runners = [r for r in available_runners 
                                       if r.runner_id != selected_runner.runner_id]
                else:
                    # 调度失败，将任务重新放回队列
                    self.queue_manager.enqueue_job(job, priority=job.priority)
                    break
            
        except Exception as e:
            self.logger.error(f"批量调度异常: {e}")
        
        return scheduled_count
    
    def _get_available_runners(self) -> List[Runner]:
        """获取可用的Runner（需要与Runner管理器集成）"""
        # 这里应该从Runner管理器获取可用的Runner
        # 暂时返回空列表
        return []


# 全局调度器实例
job_scheduler = JobScheduler()
batch_scheduler = BatchScheduler(job_scheduler, queue_manager)
