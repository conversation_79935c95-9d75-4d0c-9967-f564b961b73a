"""
集群任务管理平台 - 心跳机制
"""

import threading
import time
from typing import Dict, Set, Callable, Optional
from dataclasses import dataclass

from .logger import LoggerMixin
from .utils import get_timestamp
from .network import NetworkProtocol, NetworkMessage, MessageType


@dataclass
class HeartbeatInfo:
    """心跳信息"""
    node_id: str
    last_heartbeat: float
    status: str = "active"
    data: Dict = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class HeartbeatManager(LoggerMixin):
    """心跳管理器"""
    
    def __init__(self, network: NetworkProtocol, node_id: str, 
                 heartbeat_interval: int = 30, timeout: int = 90):
        self.network = network
        self.node_id = node_id
        self.heartbeat_interval = heartbeat_interval
        self.timeout = timeout
        self.running = False
        
        # 心跳信息存储
        self.heartbeats: Dict[str, HeartbeatInfo] = {}
        self.lock = threading.RLock()
        
        # 回调函数
        self.timeout_callbacks: Set[Callable] = set()
        self.heartbeat_callbacks: Set[Callable] = set()
        
        # 线程
        self.send_thread = None
        self.check_thread = None
        self.shutdown_event = threading.Event()
        
        # 注册消息处理器
        self.network.register_handler(MessageType.HEARTBEAT, self._handle_heartbeat)
        
        self.logger.info(f"心跳管理器初始化: {node_id}")
    
    def start(self):
        """启动心跳管理器"""
        if self.running:
            return
        
        self.logger.info("启动心跳管理器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动发送心跳线程
        self.send_thread = threading.Thread(
            target=self._send_heartbeat_loop,
            name=f"HeartbeatSender-{self.node_id}",
            daemon=True
        )
        self.send_thread.start()
        
        # 启动检查超时线程
        self.check_thread = threading.Thread(
            target=self._check_timeout_loop,
            name=f"HeartbeatChecker-{self.node_id}",
            daemon=True
        )
        self.check_thread.start()
        
        self.logger.info("心跳管理器启动成功")
    
    def shutdown(self):
        """关闭心跳管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭心跳管理器...")
        
        self.running = False
        self.shutdown_event.set()
        
        # 等待线程结束
        if self.send_thread and self.send_thread.is_alive():
            self.send_thread.join(timeout=5)
        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=5)
        
        self.logger.info("心跳管理器已关闭")
    
    def add_target(self, target_id: str):
        """添加心跳目标"""
        with self.lock:
            if target_id not in self.heartbeats:
                self.heartbeats[target_id] = HeartbeatInfo(
                    node_id=target_id,
                    last_heartbeat=get_timestamp()
                )
                self.logger.info(f"添加心跳目标: {target_id}")
    
    def remove_target(self, target_id: str):
        """移除心跳目标"""
        with self.lock:
            if target_id in self.heartbeats:
                del self.heartbeats[target_id]
                self.logger.info(f"移除心跳目标: {target_id}")
    
    def send_heartbeat(self, target_id: str, data: Dict = None):
        """发送心跳"""
        try:
            message = NetworkMessage(
                message_type=MessageType.HEARTBEAT,
                message_id=f"heartbeat-{get_timestamp()}",
                sender_id=self.node_id,
                receiver_id=target_id,
                data=data or {}
            )
            
            success = self.network.send_message(target_id, message)
            if success:
                self.logger.debug(f"心跳发送成功: {target_id}")
            else:
                self.logger.warning(f"心跳发送失败: {target_id}")
                
        except Exception as e:
            self.logger.error(f"发送心跳异常: {e}")
    
    def _handle_heartbeat(self, message: NetworkMessage):
        """处理接收到的心跳"""
        sender_id = message.sender_id
        current_time = get_timestamp()
        
        with self.lock:
            if sender_id in self.heartbeats:
                # 更新现有心跳信息
                heartbeat_info = self.heartbeats[sender_id]
                heartbeat_info.last_heartbeat = current_time
                heartbeat_info.status = "active"
                heartbeat_info.data = message.data
            else:
                # 添加新的心跳信息
                self.heartbeats[sender_id] = HeartbeatInfo(
                    node_id=sender_id,
                    last_heartbeat=current_time,
                    status="active",
                    data=message.data
                )
        
        self.logger.debug(f"收到心跳: {sender_id}")
        
        # 调用心跳回调
        for callback in self.heartbeat_callbacks:
            try:
                callback(sender_id, message.data)
            except Exception as e:
                self.logger.error(f"心跳回调异常: {e}")
    
    def _send_heartbeat_loop(self):
        """发送心跳循环"""
        self.logger.info("心跳发送循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                with self.lock:
                    targets = list(self.heartbeats.keys())
                
                # 向所有目标发送心跳
                for target_id in targets:
                    self.send_heartbeat(target_id, {
                        'status': 'active',
                        'timestamp': get_timestamp()
                    })
                
                # 等待下一次发送
                self.shutdown_event.wait(self.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"心跳发送循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _check_timeout_loop(self):
        """检查超时循环"""
        self.logger.info("心跳超时检查循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                current_time = get_timestamp()
                timeout_nodes = []
                
                with self.lock:
                    for node_id, heartbeat_info in self.heartbeats.items():
                        if (current_time - heartbeat_info.last_heartbeat > self.timeout and
                            heartbeat_info.status == "active"):
                            heartbeat_info.status = "timeout"
                            timeout_nodes.append(node_id)
                
                # 处理超时节点
                for node_id in timeout_nodes:
                    self.logger.warning(f"节点心跳超时: {node_id}")
                    
                    # 调用超时回调
                    for callback in self.timeout_callbacks:
                        try:
                            callback(node_id)
                        except Exception as e:
                            self.logger.error(f"超时回调异常: {e}")
                
                # 等待下一次检查
                self.shutdown_event.wait(10)
                
            except Exception as e:
                self.logger.error(f"心跳超时检查循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def add_timeout_callback(self, callback: Callable):
        """添加超时回调"""
        self.timeout_callbacks.add(callback)
    
    def remove_timeout_callback(self, callback: Callable):
        """移除超时回调"""
        self.timeout_callbacks.discard(callback)
    
    def add_heartbeat_callback(self, callback: Callable):
        """添加心跳回调"""
        self.heartbeat_callbacks.add(callback)
    
    def remove_heartbeat_callback(self, callback: Callable):
        """移除心跳回调"""
        self.heartbeat_callbacks.discard(callback)
    
    def get_heartbeat_info(self, node_id: str) -> Optional[HeartbeatInfo]:
        """获取心跳信息"""
        with self.lock:
            return self.heartbeats.get(node_id)
    
    def get_all_heartbeats(self) -> Dict[str, HeartbeatInfo]:
        """获取所有心跳信息"""
        with self.lock:
            return self.heartbeats.copy()
    
    def is_node_active(self, node_id: str) -> bool:
        """检查节点是否活跃"""
        with self.lock:
            heartbeat_info = self.heartbeats.get(node_id)
            if not heartbeat_info:
                return False
            
            current_time = get_timestamp()
            return (current_time - heartbeat_info.last_heartbeat <= self.timeout and
                    heartbeat_info.status == "active")
    
    def get_active_nodes(self) -> Set[str]:
        """获取活跃节点列表"""
        active_nodes = set()
        current_time = get_timestamp()
        
        with self.lock:
            for node_id, heartbeat_info in self.heartbeats.items():
                if (current_time - heartbeat_info.last_heartbeat <= self.timeout and
                    heartbeat_info.status == "active"):
                    active_nodes.add(node_id)
        
        return active_nodes
    
    def get_status(self) -> Dict:
        """获取心跳管理器状态"""
        with self.lock:
            return {
                'running': self.running,
                'node_id': self.node_id,
                'heartbeat_interval': self.heartbeat_interval,
                'timeout': self.timeout,
                'total_targets': len(self.heartbeats),
                'active_targets': len(self.get_active_nodes()),
                'heartbeats': {
                    node_id: {
                        'last_heartbeat': info.last_heartbeat,
                        'status': info.status,
                        'age': get_timestamp() - info.last_heartbeat
                    }
                    for node_id, info in self.heartbeats.items()
                }
            }
