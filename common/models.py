"""
集群任务管理平台 - 核心数据结构定义
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import time
import uuid


class JobStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消


class RunnerStatus(Enum):
    """Runner状态枚举"""
    IDLE = "idle"           # 空闲
    BUSY = "busy"           # 忙碌
    STOPPING = "stopping"   # 正在停止
    STOPPED = "stopped"     # 已停止
    ERROR = "error"         # 错误状态


class MessageType(Enum):
    """消息类型枚举"""
    HEARTBEAT = "heartbeat"
    JOB_REQUEST = "job_request"
    JOB_RESPONSE = "job_response"
    STATUS_UPDATE = "status_update"
    RUNNER_REGISTER = "runner_register"
    RUNNER_UNREGISTER = "runner_unregister"


@dataclass
class ResourceLimits:
    """资源限制配置"""
    max_cpu_percent: float = 80.0      # 最大CPU使用率(%)
    max_memory_mb: int = 1024          # 最大内存使用量(MB)
    max_runtime_seconds: int = 3600    # 最大运行时间(秒)


@dataclass
class ResourceUsage:
    """资源使用情况"""
    cpu_percent: float = 0.0           # 当前CPU使用率
    memory_mb: int = 0                 # 当前内存使用量
    runtime_seconds: int = 0           # 已运行时间
    timestamp: float = field(default_factory=time.time)


@dataclass
class Job:
    """任务数据结构"""
    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    command: str = ""                  # 执行命令
    args: List[str] = field(default_factory=list)  # 命令参数
    env: Dict[str, str] = field(default_factory=dict)  # 环境变量
    working_dir: str = ""              # 工作目录
    status: JobStatus = JobStatus.PENDING
    priority: int = 0                  # 优先级(数字越大优先级越高)
    max_retries: int = 3               # 最大重试次数
    retry_count: int = 0               # 当前重试次数
    dependencies: List[str] = field(default_factory=list)  # 依赖的任务ID
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    runner_id: Optional[str] = None    # 执行该任务的Runner ID
    result: Optional[Dict[str, Any]] = None  # 任务执行结果
    error_message: Optional[str] = None


@dataclass
class Runner:
    """Runner数据结构"""
    runner_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    host: str = ""                     # 运行主机
    port: int = 0                      # 通信端口
    status: RunnerStatus = RunnerStatus.IDLE
    resource_limits: ResourceLimits = field(default_factory=ResourceLimits)
    resource_usage: ResourceUsage = field(default_factory=ResourceUsage)
    current_job_id: Optional[str] = None
    registered_at: float = field(default_factory=time.time)
    last_heartbeat: float = field(default_factory=time.time)
    capabilities: List[str] = field(default_factory=list)  # Runner能力标签


@dataclass
class Message:
    """通信消息数据结构"""
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    message_type: MessageType = MessageType.HEARTBEAT
    timestamp: float = field(default_factory=time.time)
    sender_id: str = ""
    receiver_id: str = ""
    data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TaskQueue:
    """任务队列数据结构"""
    queue_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    jobs: List[str] = field(default_factory=list)  # 任务ID列表
    priority: int = 0
    created_at: float = field(default_factory=time.time)


@dataclass
class ClusterConfig:
    """集群配置"""
    manager_host: str = "localhost"
    manager_port: int = 8080
    heartbeat_interval: int = 30       # 心跳间隔(秒)
    job_timeout: int = 3600           # 任务超时时间(秒)
    max_concurrent_jobs: int = 10      # 最大并发任务数
    log_level: str = "INFO"
