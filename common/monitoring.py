"""
集群任务管理平台 - 实时状态监控
"""

import threading
import time
import json
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
from enum import Enum

from .logger import LoggerMixin
from .utils import get_timestamp, get_system_info
from .models import JobStatus, RunnerStatus


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"      # 计数器
    GAUGE = "gauge"          # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    SUMMARY = "summary"      # 摘要


@dataclass
class Metric:
    """指标数据"""
    name: str
    value: float
    metric_type: MetricType
    labels: Dict[str, str]
    timestamp: float
    description: str = ""


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: int
    memory_total_mb: int
    disk_percent: float
    disk_used_gb: int
    disk_total_gb: int
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: List[float]


@dataclass
class JobMetrics:
    """任务指标"""
    job_id: str
    status: str
    start_time: Optional[float]
    end_time: Optional[float]
    duration: Optional[float]
    runner_id: Optional[str]
    cpu_usage: float
    memory_usage: int
    exit_code: Optional[int]
    timestamp: float


@dataclass
class RunnerMetrics:
    """Runner指标"""
    runner_id: str
    status: str
    cpu_percent: float
    memory_mb: int
    active_jobs: int
    total_jobs_completed: int
    total_jobs_failed: int
    uptime: float
    last_heartbeat: float
    timestamp: float


class MetricsCollector(LoggerMixin):
    """指标收集器"""
    
    def __init__(self, collection_interval: int = 30):
        self.collection_interval = collection_interval
        self.running = False
        
        # 指标存储
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.current_metrics: Dict[str, Metric] = {}
        
        # 系统指标历史
        self.system_metrics_history = deque(maxlen=1000)
        self.job_metrics_history = deque(maxlen=5000)
        self.runner_metrics_history = deque(maxlen=2000)
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 收集线程
        self.collector_thread = None
        self.shutdown_event = threading.Event()
        
        # 回调函数
        self.metric_callbacks: List[Callable] = []
        
        self.logger.info("指标收集器初始化完成")
    
    def start(self):
        """启动指标收集器"""
        if self.running:
            return
        
        self.logger.info("启动指标收集器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动收集线程
        self.collector_thread = threading.Thread(
            target=self._collection_loop,
            name="MetricsCollector",
            daemon=True
        )
        self.collector_thread.start()
        
        self.logger.info("指标收集器启动成功")
    
    def shutdown(self):
        """关闭指标收集器"""
        if not self.running:
            return
        
        self.logger.info("关闭指标收集器...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.collector_thread and self.collector_thread.is_alive():
            self.collector_thread.join(timeout=10)
        
        self.logger.info("指标收集器已关闭")
    
    def _collection_loop(self):
        """收集循环"""
        self.logger.info("指标收集循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 等待下一次收集
                self.shutdown_event.wait(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"指标收集循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            import psutil
            
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            # 创建系统指标
            system_metrics = SystemMetrics(
                timestamp=get_timestamp(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used // (1024 * 1024),
                memory_total_mb=memory.total // (1024 * 1024),
                disk_percent=disk.percent,
                disk_used_gb=disk.used // (1024 * 1024 * 1024),
                disk_total_gb=disk.total // (1024 * 1024 * 1024),
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                process_count=len(psutil.pids()),
                load_average=psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            )
            
            with self.lock:
                self.system_metrics_history.append(system_metrics)
            
            # 记录单个指标
            self.record_metric("system_cpu_percent", cpu_percent, MetricType.GAUGE, {"component": "system"})
            self.record_metric("system_memory_percent", memory.percent, MetricType.GAUGE, {"component": "system"})
            self.record_metric("system_disk_percent", disk.percent, MetricType.GAUGE, {"component": "system"})
            
            self.logger.debug(f"系统指标收集完成: CPU {cpu_percent}%, 内存 {memory.percent}%")
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
    
    def record_metric(self, name: str, value: float, metric_type: MetricType, 
                     labels: Dict[str, str] = None, description: str = ""):
        """记录指标"""
        try:
            metric = Metric(
                name=name,
                value=value,
                metric_type=metric_type,
                labels=labels or {},
                timestamp=get_timestamp(),
                description=description
            )
            
            with self.lock:
                self.current_metrics[name] = metric
                self.metrics_history[name].append(metric)
            
            # 调用回调
            for callback in self.metric_callbacks:
                try:
                    callback(metric)
                except Exception as e:
                    self.logger.error(f"指标回调异常: {e}")
            
            self.logger.debug(f"指标记录: {name} = {value}")
            
        except Exception as e:
            self.logger.error(f"记录指标失败: {e}")
    
    def record_job_metrics(self, job_metrics: JobMetrics):
        """记录任务指标"""
        try:
            with self.lock:
                self.job_metrics_history.append(job_metrics)
            
            # 记录相关指标
            self.record_metric(
                f"job_duration_{job_metrics.status}",
                job_metrics.duration or 0,
                MetricType.HISTOGRAM,
                {"job_id": job_metrics.job_id, "status": job_metrics.status}
            )
            
            self.record_metric(
                "job_cpu_usage",
                job_metrics.cpu_usage,
                MetricType.GAUGE,
                {"job_id": job_metrics.job_id}
            )
            
            self.logger.debug(f"任务指标记录: {job_metrics.job_id}")
            
        except Exception as e:
            self.logger.error(f"记录任务指标失败: {e}")
    
    def record_runner_metrics(self, runner_metrics: RunnerMetrics):
        """记录Runner指标"""
        try:
            with self.lock:
                self.runner_metrics_history.append(runner_metrics)
            
            # 记录相关指标
            self.record_metric(
                "runner_cpu_percent",
                runner_metrics.cpu_percent,
                MetricType.GAUGE,
                {"runner_id": runner_metrics.runner_id}
            )
            
            self.record_metric(
                "runner_memory_mb",
                runner_metrics.memory_mb,
                MetricType.GAUGE,
                {"runner_id": runner_metrics.runner_id}
            )
            
            self.record_metric(
                "runner_active_jobs",
                runner_metrics.active_jobs,
                MetricType.GAUGE,
                {"runner_id": runner_metrics.runner_id}
            )
            
            self.logger.debug(f"Runner指标记录: {runner_metrics.runner_id}")
            
        except Exception as e:
            self.logger.error(f"记录Runner指标失败: {e}")
    
    def get_current_metric(self, name: str) -> Optional[Metric]:
        """获取当前指标值"""
        with self.lock:
            return self.current_metrics.get(name)
    
    def get_metric_history(self, name: str, limit: int = 100) -> List[Metric]:
        """获取指标历史"""
        with self.lock:
            history = self.metrics_history.get(name, deque())
            return list(history)[-limit:]
    
    def get_system_metrics(self, limit: int = 100) -> List[SystemMetrics]:
        """获取系统指标历史"""
        with self.lock:
            return list(self.system_metrics_history)[-limit:]
    
    def get_job_metrics(self, job_id: str = None, limit: int = 100) -> List[JobMetrics]:
        """获取任务指标历史"""
        with self.lock:
            metrics = list(self.job_metrics_history)[-limit:]
            if job_id:
                metrics = [m for m in metrics if m.job_id == job_id]
            return metrics
    
    def get_runner_metrics(self, runner_id: str = None, limit: int = 100) -> List[RunnerMetrics]:
        """获取Runner指标历史"""
        with self.lock:
            metrics = list(self.runner_metrics_history)[-limit:]
            if runner_id:
                metrics = [m for m in metrics if m.runner_id == runner_id]
            return metrics
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self.lock:
            return {
                "total_metrics": len(self.current_metrics),
                "system_metrics_count": len(self.system_metrics_history),
                "job_metrics_count": len(self.job_metrics_history),
                "runner_metrics_count": len(self.runner_metrics_history),
                "collection_interval": self.collection_interval,
                "running": self.running
            }
    
    def add_metric_callback(self, callback: Callable):
        """添加指标回调"""
        self.metric_callbacks.append(callback)
    
    def export_metrics(self, format: str = "json") -> str:
        """导出指标数据"""
        with self.lock:
            if format.lower() == "json":
                data = {
                    "current_metrics": {
                        name: asdict(metric) for name, metric in self.current_metrics.items()
                    },
                    "system_metrics": [asdict(m) for m in self.system_metrics_history],
                    "job_metrics": [asdict(m) for m in self.job_metrics_history],
                    "runner_metrics": [asdict(m) for m in self.runner_metrics_history]
                }
                return json.dumps(data, indent=2)
            else:
                raise ValueError(f"不支持的导出格式: {format}")


class StatusMonitor(LoggerMixin):
    """状态监控器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.running = False
        
        # 状态缓存
        self.cluster_status = {}
        self.runner_statuses = {}
        self.job_statuses = {}
        
        # 状态更新回调
        self.status_callbacks: List[Callable] = []
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        self.logger.info("状态监控器初始化完成")
    
    def start(self):
        """启动状态监控器"""
        if self.running:
            return
        
        self.logger.info("启动状态监控器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="StatusMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("状态监控器启动成功")
    
    def shutdown(self):
        """关闭状态监控器"""
        if not self.running:
            return
        
        self.logger.info("关闭状态监控器...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        self.logger.info("状态监控器已关闭")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("状态监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 更新集群状态
                self._update_cluster_status()
                
                # 等待下一次更新
                self.shutdown_event.wait(10)
                
            except Exception as e:
                self.logger.error(f"状态监控循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _update_cluster_status(self):
        """更新集群状态"""
        try:
            # 获取最新的系统指标
            system_metrics = self.metrics_collector.get_system_metrics(limit=1)
            
            if system_metrics:
                latest_metrics = system_metrics[0]
                
                with self.lock:
                    self.cluster_status = {
                        "timestamp": latest_metrics.timestamp,
                        "cpu_percent": latest_metrics.cpu_percent,
                        "memory_percent": latest_metrics.memory_percent,
                        "disk_percent": latest_metrics.disk_percent,
                        "process_count": latest_metrics.process_count,
                        "load_average": latest_metrics.load_average,
                        "status": self._determine_cluster_health(latest_metrics)
                    }
                
                # 调用状态更新回调
                for callback in self.status_callbacks:
                    try:
                        callback("cluster", self.cluster_status)
                    except Exception as e:
                        self.logger.error(f"状态回调异常: {e}")
            
        except Exception as e:
            self.logger.error(f"更新集群状态失败: {e}")
    
    def _determine_cluster_health(self, metrics: SystemMetrics) -> str:
        """判断集群健康状态"""
        if metrics.cpu_percent > 90 or metrics.memory_percent > 90:
            return "critical"
        elif metrics.cpu_percent > 80 or metrics.memory_percent > 80:
            return "warning"
        else:
            return "healthy"
    
    def update_runner_status(self, runner_id: str, status: Dict[str, Any]):
        """更新Runner状态"""
        with self.lock:
            self.runner_statuses[runner_id] = {
                **status,
                "last_update": get_timestamp()
            }
        
        # 调用状态更新回调
        for callback in self.status_callbacks:
            try:
                callback("runner", {"runner_id": runner_id, **status})
            except Exception as e:
                self.logger.error(f"状态回调异常: {e}")
    
    def update_job_status(self, job_id: str, status: Dict[str, Any]):
        """更新任务状态"""
        with self.lock:
            self.job_statuses[job_id] = {
                **status,
                "last_update": get_timestamp()
            }
        
        # 调用状态更新回调
        for callback in self.status_callbacks:
            try:
                callback("job", {"job_id": job_id, **status})
            except Exception as e:
                self.logger.error(f"状态回调异常: {e}")
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        with self.lock:
            return self.cluster_status.copy()
    
    def get_runner_status(self, runner_id: str) -> Optional[Dict[str, Any]]:
        """获取Runner状态"""
        with self.lock:
            return self.runner_statuses.get(runner_id, {}).copy()
    
    def get_all_runner_statuses(self) -> Dict[str, Dict[str, Any]]:
        """获取所有Runner状态"""
        with self.lock:
            return {rid: status.copy() for rid, status in self.runner_statuses.items()}
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            return self.job_statuses.get(job_id, {}).copy()
    
    def get_all_job_statuses(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        with self.lock:
            return {jid: status.copy() for jid, status in self.job_statuses.items()}
    
    def add_status_callback(self, callback: Callable):
        """添加状态更新回调"""
        self.status_callbacks.append(callback)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        with self.lock:
            return {
                "cluster_status": self.cluster_status,
                "total_runners": len(self.runner_statuses),
                "active_runners": len([s for s in self.runner_statuses.values() 
                                     if s.get("status") == "active"]),
                "total_jobs": len(self.job_statuses),
                "running_jobs": len([s for s in self.job_statuses.values() 
                                   if s.get("status") == "running"]),
                "completed_jobs": len([s for s in self.job_statuses.values() 
                                     if s.get("status") == "completed"])
            }


# 全局实例
metrics_collector = MetricsCollector()
status_monitor = StatusMonitor(metrics_collector)
