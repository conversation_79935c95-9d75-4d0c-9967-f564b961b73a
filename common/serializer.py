"""
集群任务管理平台 - 序列化工具模块
"""

import json
import pickle
from typing import Any, Dict, Type, Union
from datetime import datetime
from dataclasses import asdict, is_dataclass
from enum import Enum

from .models import Job, Runner, Message, TaskQueue, ResourceLimits, ResourceUsage


class EnhancedJSONEncoder(json.JSONEncoder):
    """增强的JSON编码器，支持更多数据类型"""
    
    def default(self, obj):
        if is_dataclass(obj):
            return asdict(obj)
        elif isinstance(obj, Enum):
            return obj.value
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)


class Serializer:
    """序列化工具类"""
    
    @staticmethod
    def to_json(obj: Any, indent: int = None) -> str:
        """将对象序列化为JSON字符串"""
        try:
            return json.dumps(obj, cls=EnhancedJSONEncoder, indent=indent, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"JSON序列化失败: {e}")
    
    @staticmethod
    def from_json(json_str: str, target_type: Type = None) -> Any:
        """从JSON字符串反序列化对象"""
        try:
            data = json.loads(json_str)
            
            if target_type and is_dataclass(target_type):
                return Serializer._dict_to_dataclass(data, target_type)
            
            return data
        except Exception as e:
            raise ValueError(f"JSON反序列化失败: {e}")
    
    @staticmethod
    def _dict_to_dataclass(data: Dict, target_type: Type):
        """将字典转换为数据类实例"""
        if not is_dataclass(target_type):
            return data
        
        # 获取数据类的字段信息
        import inspect
        sig = inspect.signature(target_type)
        
        # 准备构造参数
        kwargs = {}
        for param_name, param in sig.parameters.items():
            if param_name in data:
                value = data[param_name]
                
                # 处理嵌套的数据类
                if hasattr(param.annotation, '__origin__'):
                    # 处理泛型类型（如List[str]）
                    kwargs[param_name] = value
                elif is_dataclass(param.annotation):
                    kwargs[param_name] = Serializer._dict_to_dataclass(value, param.annotation)
                elif isinstance(param.annotation, type) and issubclass(param.annotation, Enum):
                    kwargs[param_name] = param.annotation(value)
                else:
                    kwargs[param_name] = value
        
        return target_type(**kwargs)
    
    @staticmethod
    def to_bytes(obj: Any) -> bytes:
        """将对象序列化为字节"""
        try:
            return pickle.dumps(obj)
        except Exception as e:
            raise ValueError(f"字节序列化失败: {e}")
    
    @staticmethod
    def from_bytes(data: bytes) -> Any:
        """从字节反序列化对象"""
        try:
            return pickle.loads(data)
        except Exception as e:
            raise ValueError(f"字节反序列化失败: {e}")


class MessageSerializer:
    """消息序列化器"""
    
    @staticmethod
    def serialize_message(message: Message) -> str:
        """序列化消息对象"""
        return Serializer.to_json(message)
    
    @staticmethod
    def deserialize_message(json_str: str) -> Message:
        """反序列化消息对象"""
        return Serializer.from_json(json_str, Message)
    
    @staticmethod
    def serialize_job(job: Job) -> str:
        """序列化任务对象"""
        return Serializer.to_json(job)
    
    @staticmethod
    def deserialize_job(json_str: str) -> Job:
        """反序列化任务对象"""
        return Serializer.from_json(json_str, Job)
    
    @staticmethod
    def serialize_runner(runner: Runner) -> str:
        """序列化Runner对象"""
        return Serializer.to_json(runner)
    
    @staticmethod
    def deserialize_runner(json_str: str) -> Runner:
        """反序列化Runner对象"""
        return Serializer.from_json(json_str, Runner)


class DataPersistence:
    """数据持久化工具"""
    
    @staticmethod
    def save_to_file(obj: Any, filepath: str, format: str = 'json'):
        """保存对象到文件"""
        import os
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        if format.lower() == 'json':
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(Serializer.to_json(obj, indent=2))
        elif format.lower() == 'pickle':
            with open(filepath, 'wb') as f:
                f.write(Serializer.to_bytes(obj))
        else:
            raise ValueError(f"不支持的格式: {format}")
    
    @staticmethod
    def load_from_file(filepath: str, format: str = 'json', target_type: Type = None) -> Any:
        """从文件加载对象"""
        if format.lower() == 'json':
            with open(filepath, 'r', encoding='utf-8') as f:
                return Serializer.from_json(f.read(), target_type)
        elif format.lower() == 'pickle':
            with open(filepath, 'rb') as f:
                return Serializer.from_bytes(f.read())
        else:
            raise ValueError(f"不支持的格式: {format}")


class ConfigSerializer:
    """配置序列化器"""
    
    @staticmethod
    def serialize_config(config: Dict[str, Any]) -> str:
        """序列化配置字典"""
        return json.dumps(config, indent=2, ensure_ascii=False)
    
    @staticmethod
    def deserialize_config(json_str: str) -> Dict[str, Any]:
        """反序列化配置字典"""
        return json.loads(json_str)


# 便捷函数
def serialize(obj: Any, format: str = 'json') -> Union[str, bytes]:
    """通用序列化函数"""
    if format.lower() == 'json':
        return Serializer.to_json(obj)
    elif format.lower() == 'bytes':
        return Serializer.to_bytes(obj)
    else:
        raise ValueError(f"不支持的序列化格式: {format}")


def deserialize(data: Union[str, bytes], target_type: Type = None) -> Any:
    """通用反序列化函数"""
    if isinstance(data, str):
        return Serializer.from_json(data, target_type)
    elif isinstance(data, bytes):
        return Serializer.from_bytes(data)
    else:
        raise ValueError(f"不支持的数据类型: {type(data)}")


# 示例使用
if __name__ == "__main__":
    from .models import Job, JobStatus, ResourceLimits
    
    # 创建测试对象
    job = Job(
        name="测试任务",
        description="这是一个测试任务",
        command="python",
        args=["test.py"],
        status=JobStatus.PENDING
    )
    
    # 序列化
    json_str = Serializer.to_json(job)
    print("序列化结果:")
    print(json_str)
    
    # 反序列化
    restored_job = Serializer.from_json(json_str, Job)
    print("\n反序列化结果:")
    print(f"任务名称: {restored_job.name}")
    print(f"任务状态: {restored_job.status}")
    
    # 保存到文件
    DataPersistence.save_to_file(job, "test_job.json")
    loaded_job = DataPersistence.load_from_file("test_job.json", target_type=Job)
    print(f"\n从文件加载的任务: {loaded_job.name}")
