"""
集群任务管理平台 - 告警机制
"""

import threading
import time
import smtplib
import json
from typing import Dict, List, Optional, Callable, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from collections import deque

from .logger import LoggerMixin
from .utils import get_timestamp


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertType(Enum):
    """告警类型"""
    SYSTEM_RESOURCE = "system_resource"
    RUNNER_OFFLINE = "runner_offline"
    JOB_FAILED = "job_failed"
    QUEUE_OVERFLOW = "queue_overflow"
    NETWORK_ERROR = "network_error"
    CUSTOM = "custom"


@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    alert_type: AlertType
    level: AlertLevel
    title: str
    message: str
    source: str
    timestamp: float
    resolved: bool = False
    resolved_at: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class AlertRule:
    """告警规则"""
    
    def __init__(self, rule_id: str, name: str, condition: Callable[[Dict], bool],
                 alert_type: AlertType, level: AlertLevel, message_template: str):
        self.rule_id = rule_id
        self.name = name
        self.condition = condition
        self.alert_type = alert_type
        self.level = level
        self.message_template = message_template
        self.enabled = True
        self.cooldown_seconds = 300  # 5分钟冷却期
        self.last_triggered = 0
    
    def should_trigger(self, data: Dict[str, Any]) -> bool:
        """检查是否应该触发告警"""
        if not self.enabled:
            return False
        
        # 检查冷却期
        current_time = get_timestamp()
        if current_time - self.last_triggered < self.cooldown_seconds:
            return False
        
        # 检查条件
        try:
            return self.condition(data)
        except Exception:
            return False
    
    def trigger(self, data: Dict[str, Any]) -> Alert:
        """触发告警"""
        self.last_triggered = get_timestamp()
        
        # 格式化消息
        try:
            message = self.message_template.format(**data)
        except:
            message = self.message_template
        
        return Alert(
            alert_id=f"{self.rule_id}_{int(self.last_triggered)}",
            alert_type=self.alert_type,
            level=self.level,
            title=self.name,
            message=message,
            source=data.get('source', 'unknown'),
            timestamp=self.last_triggered,
            metadata=data
        )


class AlertManager(LoggerMixin):
    """告警管理器"""
    
    def __init__(self):
        self.running = False
        
        # 告警存储
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history = deque(maxlen=10000)
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 告警处理器
        self.alert_handlers: List[Callable] = []
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_alerts': 0,
            'active_alerts': 0,
            'resolved_alerts': 0,
            'alerts_by_level': {level.value: 0 for level in AlertLevel},
            'alerts_by_type': {alert_type.value: 0 for alert_type in AlertType}
        }
        
        # 注册默认规则
        self._register_default_rules()
        
        self.logger.info("告警管理器初始化完成")
    
    def _register_default_rules(self):
        """注册默认告警规则"""
        # CPU使用率告警
        self.add_rule(AlertRule(
            rule_id="high_cpu",
            name="CPU使用率过高",
            condition=lambda data: data.get('cpu_percent', 0) > 90,
            alert_type=AlertType.SYSTEM_RESOURCE,
            level=AlertLevel.WARNING,
            message_template="CPU使用率达到 {cpu_percent:.1f}%，超过阈值"
        ))
        
        # 内存使用率告警
        self.add_rule(AlertRule(
            rule_id="high_memory",
            name="内存使用率过高",
            condition=lambda data: data.get('memory_percent', 0) > 90,
            alert_type=AlertType.SYSTEM_RESOURCE,
            level=AlertLevel.WARNING,
            message_template="内存使用率达到 {memory_percent:.1f}%，超过阈值"
        ))
        
        # 磁盘使用率告警
        self.add_rule(AlertRule(
            rule_id="high_disk",
            name="磁盘使用率过高",
            condition=lambda data: data.get('disk_percent', 0) > 95,
            alert_type=AlertType.SYSTEM_RESOURCE,
            level=AlertLevel.ERROR,
            message_template="磁盘使用率达到 {disk_percent:.1f}%，空间不足"
        ))
        
        # Runner离线告警
        self.add_rule(AlertRule(
            rule_id="runner_offline",
            name="Runner离线",
            condition=lambda data: data.get('status') == 'offline',
            alert_type=AlertType.RUNNER_OFFLINE,
            level=AlertLevel.ERROR,
            message_template="Runner {runner_id} 已离线"
        ))
        
        # 任务失败告警
        self.add_rule(AlertRule(
            rule_id="job_failed",
            name="任务执行失败",
            condition=lambda data: data.get('status') == 'failed',
            alert_type=AlertType.JOB_FAILED,
            level=AlertLevel.WARNING,
            message_template="任务 {job_id} 执行失败: {error_message}"
        ))
    
    def start(self):
        """启动告警管理器"""
        if self.running:
            return
        
        self.logger.info("启动告警管理器...")
        self.running = True
        self.logger.info("告警管理器启动成功")
    
    def shutdown(self):
        """关闭告警管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭告警管理器...")
        self.running = False
        self.logger.info("告警管理器已关闭")
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self.lock:
            self.alert_rules[rule.rule_id] = rule
            self.logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_id: str):
        """移除告警规则"""
        with self.lock:
            if rule_id in self.alert_rules:
                rule = self.alert_rules.pop(rule_id)
                self.logger.info(f"移除告警规则: {rule.name}")
    
    def enable_rule(self, rule_id: str):
        """启用告警规则"""
        with self.lock:
            if rule_id in self.alert_rules:
                self.alert_rules[rule_id].enabled = True
                self.logger.info(f"启用告警规则: {rule_id}")
    
    def disable_rule(self, rule_id: str):
        """禁用告警规则"""
        with self.lock:
            if rule_id in self.alert_rules:
                self.alert_rules[rule_id].enabled = False
                self.logger.info(f"禁用告警规则: {rule_id}")
    
    def check_alerts(self, data: Dict[str, Any]):
        """检查告警条件"""
        if not self.running:
            return
        
        triggered_alerts = []
        
        with self.lock:
            for rule in self.alert_rules.values():
                if rule.should_trigger(data):
                    alert = rule.trigger(data)
                    triggered_alerts.append(alert)
        
        # 处理触发的告警
        for alert in triggered_alerts:
            self._handle_alert(alert)
    
    def _handle_alert(self, alert: Alert):
        """处理告警"""
        with self.lock:
            # 添加到活动告警
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            
            # 更新统计
            self.stats['total_alerts'] += 1
            self.stats['active_alerts'] = len(self.active_alerts)
            self.stats['alerts_by_level'][alert.level.value] += 1
            self.stats['alerts_by_type'][alert.alert_type.value] += 1
        
        self.logger.warning(f"触发告警: {alert.title} - {alert.message}")
        
        # 调用告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"告警处理器异常: {e}")
    
    def resolve_alert(self, alert_id: str, resolution_note: str = ""):
        """解决告警"""
        with self.lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = get_timestamp()
                alert.metadata['resolution_note'] = resolution_note
                
                # 从活动告警中移除
                del self.active_alerts[alert_id]
                
                # 更新统计
                self.stats['active_alerts'] = len(self.active_alerts)
                self.stats['resolved_alerts'] += 1
                
                self.logger.info(f"告警已解决: {alert.title}")
                return True
        
        return False
    
    def get_active_alerts(self, level: AlertLevel = None, 
                         alert_type: AlertType = None) -> List[Alert]:
        """获取活动告警"""
        with self.lock:
            alerts = list(self.active_alerts.values())
            
            if level:
                alerts = [a for a in alerts if a.level == level]
            if alert_type:
                alerts = [a for a in alerts if a.alert_type == alert_type]
            
            # 按时间倒序排列
            alerts.sort(key=lambda x: x.timestamp, reverse=True)
            return alerts
    
    def get_alert_history(self, limit: int = 100, level: AlertLevel = None,
                         alert_type: AlertType = None) -> List[Alert]:
        """获取告警历史"""
        with self.lock:
            alerts = list(self.alert_history)
            
            if level:
                alerts = [a for a in alerts if a.level == level]
            if alert_type:
                alerts = [a for a in alerts if a.alert_type == alert_type]
            
            # 按时间倒序排列并限制数量
            alerts.sort(key=lambda x: x.timestamp, reverse=True)
            return alerts[:limit]
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """获取告警统计"""
        with self.lock:
            return self.stats.copy()
    
    def add_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
        self.logger.info("添加告警处理器")
    
    def create_custom_alert(self, title: str, message: str, level: AlertLevel,
                          source: str = "manual", metadata: Dict = None) -> str:
        """创建自定义告警"""
        alert = Alert(
            alert_id=f"custom_{int(get_timestamp())}",
            alert_type=AlertType.CUSTOM,
            level=level,
            title=title,
            message=message,
            source=source,
            timestamp=get_timestamp(),
            metadata=metadata or {}
        )
        
        self._handle_alert(alert)
        return alert.alert_id


class EmailAlertHandler(LoggerMixin):
    """邮件告警处理器"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, recipients: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.recipients = recipients
        self.enabled = True
        
        self.logger.info(f"邮件告警处理器初始化: {smtp_server}:{smtp_port}")
    
    def __call__(self, alert: Alert):
        """处理告警"""
        if not self.enabled:
            return
        
        # 只处理ERROR和CRITICAL级别的告警
        if alert.level not in [AlertLevel.ERROR, AlertLevel.CRITICAL]:
            return
        
        try:
            self._send_email(alert)
        except Exception as e:
            self.logger.error(f"发送告警邮件失败: {e}")
    
    def _send_email(self, alert: Alert):
        """发送告警邮件"""
        # 创建邮件
        msg = MimeMultipart()
        msg['From'] = self.username
        msg['To'] = ', '.join(self.recipients)
        msg['Subject'] = f"[{alert.level.value.upper()}] {alert.title}"
        
        # 邮件内容
        body = f"""
告警详情:

级别: {alert.level.value.upper()}
类型: {alert.alert_type.value}
来源: {alert.source}
时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.timestamp))}

消息:
{alert.message}

元数据:
{json.dumps(alert.metadata, indent=2, ensure_ascii=False)}
        """
        
        msg.attach(MimeText(body, 'plain', 'utf-8'))
        
        # 发送邮件
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)
        
        self.logger.info(f"告警邮件已发送: {alert.title}")


class WebhookAlertHandler(LoggerMixin):
    """Webhook告警处理器"""
    
    def __init__(self, webhook_url: str, headers: Dict[str, str] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {'Content-Type': 'application/json'}
        self.enabled = True
        
        self.logger.info(f"Webhook告警处理器初始化: {webhook_url}")
    
    def __call__(self, alert: Alert):
        """处理告警"""
        if not self.enabled:
            return
        
        try:
            self._send_webhook(alert)
        except Exception as e:
            self.logger.error(f"发送Webhook告警失败: {e}")
    
    def _send_webhook(self, alert: Alert):
        """发送Webhook告警"""
        import requests
        
        # 构建payload
        payload = {
            'alert_id': alert.alert_id,
            'type': alert.alert_type.value,
            'level': alert.level.value,
            'title': alert.title,
            'message': alert.message,
            'source': alert.source,
            'timestamp': alert.timestamp,
            'metadata': alert.metadata
        }
        
        # 发送请求
        response = requests.post(
            self.webhook_url,
            json=payload,
            headers=self.headers,
            timeout=10
        )
        
        response.raise_for_status()
        self.logger.info(f"Webhook告警已发送: {alert.title}")


# 全局告警管理器实例
alert_manager = AlertManager()
