"""
集群任务管理平台 - 负载均衡算法
"""

import random
import threading
import time
from typing import Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque
from abc import ABC, abstractmethod

from .logger import LoggerMixin
from .models import Runner, Job, RunnerStatus
from .utils import get_timestamp


class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"           # 轮询
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"  # 加权轮询
    LEAST_CONNECTIONS = "least_connections"        # 最少连接
    WEIGHTED_LEAST_CONNECTIONS = "weighted_least_connections"  # 加权最少连接
    RESOURCE_BASED = "resource_based"     # 基于资源
    RESPONSE_TIME = "response_time"       # 响应时间
    RANDOM = "random"                     # 随机
    CONSISTENT_HASH = "consistent_hash"   # 一致性哈希


@dataclass
class LoadMetrics:
    """负载指标"""
    runner_id: str
    cpu_percent: float
    memory_percent: float
    active_jobs: int
    total_jobs: int
    avg_response_time: float
    last_update: float
    weight: float = 1.0


class LoadBalancer(ABC, LoggerMixin):
    """负载均衡器基类"""
    
    def __init__(self, strategy: LoadBalanceStrategy):
        self.strategy = strategy
        self.runners: Dict[str, Runner] = {}
        self.load_metrics: Dict[str, LoadMetrics] = {}
        self.lock = threading.RLock()
        
        self.logger.info(f"负载均衡器初始化: {strategy.value}")
    
    @abstractmethod
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        """选择Runner"""
        pass
    
    def update_runner(self, runner: Runner):
        """更新Runner信息"""
        with self.lock:
            self.runners[runner.runner_id] = runner
            
            # 更新负载指标
            if runner.runner_id not in self.load_metrics:
                self.load_metrics[runner.runner_id] = LoadMetrics(
                    runner_id=runner.runner_id,
                    cpu_percent=0,
                    memory_percent=0,
                    active_jobs=0,
                    total_jobs=0,
                    avg_response_time=0,
                    last_update=get_timestamp()
                )
            
            metrics = self.load_metrics[runner.runner_id]
            metrics.cpu_percent = runner.resource_usage.cpu_percent
            metrics.memory_percent = (runner.resource_usage.memory_mb / 
                                    runner.resource_limits.max_memory_mb * 100)
            metrics.last_update = get_timestamp()
    
    def remove_runner(self, runner_id: str):
        """移除Runner"""
        with self.lock:
            self.runners.pop(runner_id, None)
            self.load_metrics.pop(runner_id, None)
    
    def update_job_metrics(self, runner_id: str, job_completed: bool = False, 
                          response_time: float = None):
        """更新任务指标"""
        with self.lock:
            if runner_id in self.load_metrics:
                metrics = self.load_metrics[runner_id]
                
                if job_completed:
                    metrics.total_jobs += 1
                    if response_time:
                        # 计算平均响应时间
                        if metrics.avg_response_time == 0:
                            metrics.avg_response_time = response_time
                        else:
                            metrics.avg_response_time = (
                                metrics.avg_response_time * 0.8 + response_time * 0.2
                            )
                
                metrics.last_update = get_timestamp()
    
    def get_load_score(self, runner_id: str) -> float:
        """计算负载分数"""
        with self.lock:
            if runner_id not in self.load_metrics:
                return float('inf')
            
            metrics = self.load_metrics[runner_id]
            
            # 综合负载分数 (0-1，越小越好)
            cpu_score = metrics.cpu_percent / 100.0
            memory_score = metrics.memory_percent / 100.0
            job_score = min(1.0, metrics.active_jobs / 10.0)  # 假设最大10个并发任务
            
            return (cpu_score * 0.4 + memory_score * 0.3 + job_score * 0.3)


class RoundRobinBalancer(LoadBalancer):
    """轮询负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.ROUND_ROBIN)
        self.current_index = 0
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            # 轮询选择
            selected_runner = available_runners[self.current_index % len(available_runners)]
            self.current_index = (self.current_index + 1) % len(available_runners)
            
            self.logger.debug(f"轮询选择Runner: {selected_runner.runner_id}")
            return selected_runner


class WeightedRoundRobinBalancer(LoadBalancer):
    """加权轮询负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN)
        self.current_weights: Dict[str, int] = {}
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            # 计算权重
            max_weight = 0
            selected_runner = None
            
            for runner in available_runners:
                runner_id = runner.runner_id
                
                # 获取权重（基于资源容量）
                weight = self._calculate_weight(runner)
                
                # 增加当前权重
                self.current_weights[runner_id] = self.current_weights.get(runner_id, 0) + weight
                
                # 选择权重最大的
                if self.current_weights[runner_id] > max_weight:
                    max_weight = self.current_weights[runner_id]
                    selected_runner = runner
            
            if selected_runner:
                # 减少选中Runner的权重
                total_weight = sum(self._calculate_weight(r) for r in available_runners)
                self.current_weights[selected_runner.runner_id] -= total_weight
                
                self.logger.debug(f"加权轮询选择Runner: {selected_runner.runner_id}")
            
            return selected_runner
    
    def _calculate_weight(self, runner: Runner) -> int:
        """计算Runner权重"""
        # 基于CPU核心数和内存大小计算权重
        cpu_cores = getattr(runner, 'cpu_cores', 1)
        memory_gb = runner.resource_limits.max_memory_mb // 1024
        return max(1, cpu_cores + memory_gb // 2)


class LeastConnectionsBalancer(LoadBalancer):
    """最少连接负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.LEAST_CONNECTIONS)
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            min_connections = float('inf')
            selected_runner = None
            
            for runner in available_runners:
                metrics = self.load_metrics.get(runner.runner_id)
                connections = metrics.active_jobs if metrics else 0
                
                if connections < min_connections:
                    min_connections = connections
                    selected_runner = runner
            
            if selected_runner:
                # 更新活动任务数
                metrics = self.load_metrics.get(selected_runner.runner_id)
                if metrics:
                    metrics.active_jobs += 1
                
                self.logger.debug(f"最少连接选择Runner: {selected_runner.runner_id}")
            
            return selected_runner


class ResourceBasedBalancer(LoadBalancer):
    """基于资源的负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.RESOURCE_BASED)
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            best_score = float('inf')
            selected_runner = None
            
            for runner in available_runners:
                score = self.get_load_score(runner.runner_id)
                
                # 考虑任务的资源需求
                estimated_cpu = getattr(job, 'estimated_cpu_percent', 10.0)
                estimated_memory = getattr(job, 'estimated_memory_mb', 100)
                
                # 检查资源是否足够
                if not self._has_sufficient_resources(runner, estimated_cpu, estimated_memory):
                    continue
                
                if score < best_score:
                    best_score = score
                    selected_runner = runner
            
            if selected_runner:
                self.logger.debug(f"资源优化选择Runner: {selected_runner.runner_id}")
            
            return selected_runner
    
    def _has_sufficient_resources(self, runner: Runner, cpu_percent: float, 
                                 memory_mb: int) -> bool:
        """检查资源是否充足"""
        current_cpu = runner.resource_usage.cpu_percent
        current_memory = runner.resource_usage.memory_mb
        
        return (current_cpu + cpu_percent <= runner.resource_limits.max_cpu_percent * 0.9 and
                current_memory + memory_mb <= runner.resource_limits.max_memory_mb * 0.9)


class ResponseTimeBalancer(LoadBalancer):
    """基于响应时间的负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.RESPONSE_TIME)
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            best_time = float('inf')
            selected_runner = None
            
            for runner in available_runners:
                metrics = self.load_metrics.get(runner.runner_id)
                avg_time = metrics.avg_response_time if metrics else 0
                
                # 如果没有历史数据，给予较高优先级
                if avg_time == 0:
                    avg_time = 1.0
                
                if avg_time < best_time:
                    best_time = avg_time
                    selected_runner = runner
            
            if selected_runner:
                self.logger.debug(f"响应时间优化选择Runner: {selected_runner.runner_id}")
            
            return selected_runner


class RandomBalancer(LoadBalancer):
    """随机负载均衡器"""
    
    def __init__(self):
        super().__init__(LoadBalanceStrategy.RANDOM)
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        selected_runner = random.choice(available_runners)
        self.logger.debug(f"随机选择Runner: {selected_runner.runner_id}")
        return selected_runner


class ConsistentHashBalancer(LoadBalancer):
    """一致性哈希负载均衡器"""
    
    def __init__(self, virtual_nodes: int = 150):
        super().__init__(LoadBalanceStrategy.CONSISTENT_HASH)
        self.virtual_nodes = virtual_nodes
        self.hash_ring: Dict[int, str] = {}
        self.sorted_keys: List[int] = []
    
    def update_runner(self, runner: Runner):
        super().update_runner(runner)
        self._rebuild_hash_ring()
    
    def remove_runner(self, runner_id: str):
        super().remove_runner(runner_id)
        self._rebuild_hash_ring()
    
    def _rebuild_hash_ring(self):
        """重建哈希环"""
        with self.lock:
            self.hash_ring.clear()
            
            for runner_id in self.runners.keys():
                for i in range(self.virtual_nodes):
                    virtual_key = hash(f"{runner_id}:{i}")
                    self.hash_ring[virtual_key] = runner_id
            
            self.sorted_keys = sorted(self.hash_ring.keys())
    
    def select_runner(self, job: Job, available_runners: List[Runner]) -> Optional[Runner]:
        if not available_runners:
            return None
        
        with self.lock:
            if not self.sorted_keys:
                return available_runners[0]
            
            # 计算任务的哈希值
            job_hash = hash(job.job_id)
            
            # 在哈希环上查找
            for key in self.sorted_keys:
                if job_hash <= key:
                    runner_id = self.hash_ring[key]
                    # 检查Runner是否可用
                    for runner in available_runners:
                        if runner.runner_id == runner_id:
                            self.logger.debug(f"一致性哈希选择Runner: {runner_id}")
                            return runner
                    break
            
            # 如果没找到，选择第一个可用的
            runner_id = self.hash_ring[self.sorted_keys[0]]
            for runner in available_runners:
                if runner.runner_id == runner_id:
                    return runner
            
            return available_runners[0]


class LoadBalancerManager(LoggerMixin):
    """负载均衡器管理器"""
    
    def __init__(self, default_strategy: LoadBalanceStrategy = LoadBalanceStrategy.RESOURCE_BASED):
        self.current_strategy = default_strategy
        self.balancers: Dict[LoadBalanceStrategy, LoadBalancer] = {}
        self.lock = threading.RLock()
        
        # 初始化所有负载均衡器
        self._initialize_balancers()
        
        # 统计信息
        self.stats = {
            'total_selections': 0,
            'selections_by_strategy': defaultdict(int),
            'avg_selection_time': 0.0
        }
        
        self.logger.info(f"负载均衡器管理器初始化，默认策略: {default_strategy.value}")
    
    def _initialize_balancers(self):
        """初始化所有负载均衡器"""
        self.balancers[LoadBalanceStrategy.ROUND_ROBIN] = RoundRobinBalancer()
        self.balancers[LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN] = WeightedRoundRobinBalancer()
        self.balancers[LoadBalanceStrategy.LEAST_CONNECTIONS] = LeastConnectionsBalancer()
        self.balancers[LoadBalanceStrategy.RESOURCE_BASED] = ResourceBasedBalancer()
        self.balancers[LoadBalanceStrategy.RESPONSE_TIME] = ResponseTimeBalancer()
        self.balancers[LoadBalanceStrategy.RANDOM] = RandomBalancer()
        self.balancers[LoadBalanceStrategy.CONSISTENT_HASH] = ConsistentHashBalancer()
    
    def select_runner(self, job: Job, available_runners: List[Runner], 
                     strategy: LoadBalanceStrategy = None) -> Optional[Runner]:
        """选择Runner"""
        start_time = time.time()
        
        strategy = strategy or self.current_strategy
        
        with self.lock:
            balancer = self.balancers.get(strategy)
            if not balancer:
                self.logger.error(f"未知的负载均衡策略: {strategy}")
                return None
            
            selected_runner = balancer.select_runner(job, available_runners)
            
            # 更新统计
            selection_time = time.time() - start_time
            self.stats['total_selections'] += 1
            self.stats['selections_by_strategy'][strategy.value] += 1
            
            # 更新平均选择时间
            total = self.stats['total_selections']
            current_avg = self.stats['avg_selection_time']
            self.stats['avg_selection_time'] = (
                (current_avg * (total - 1) + selection_time) / total
            )
            
            if selected_runner:
                self.logger.debug(f"负载均衡选择完成: {selected_runner.runner_id} ({strategy.value})")
            
            return selected_runner
    
    def update_runner(self, runner: Runner):
        """更新所有负载均衡器中的Runner信息"""
        with self.lock:
            for balancer in self.balancers.values():
                balancer.update_runner(runner)
    
    def remove_runner(self, runner_id: str):
        """从所有负载均衡器中移除Runner"""
        with self.lock:
            for balancer in self.balancers.values():
                balancer.remove_runner(runner_id)
    
    def update_job_metrics(self, runner_id: str, job_completed: bool = False, 
                          response_time: float = None):
        """更新任务指标"""
        with self.lock:
            for balancer in self.balancers.values():
                balancer.update_job_metrics(runner_id, job_completed, response_time)
    
    def set_strategy(self, strategy: LoadBalanceStrategy):
        """设置负载均衡策略"""
        with self.lock:
            old_strategy = self.current_strategy
            self.current_strategy = strategy
            self.logger.info(f"负载均衡策略变更: {old_strategy.value} -> {strategy.value}")
    
    def get_current_strategy(self) -> LoadBalanceStrategy:
        """获取当前策略"""
        return self.current_strategy
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        with self.lock:
            return {
                'current_strategy': self.current_strategy.value,
                **self.stats,
                'selections_by_strategy': dict(self.stats['selections_by_strategy'])
            }
    
    def get_load_metrics(self) -> Dict[str, LoadMetrics]:
        """获取所有Runner的负载指标"""
        with self.lock:
            balancer = self.balancers[self.current_strategy]
            return balancer.load_metrics.copy()


# 全局负载均衡器管理器实例
load_balancer_manager = LoadBalancerManager()
