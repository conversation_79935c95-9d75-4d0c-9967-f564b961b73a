"""
集群任务管理平台 - 失败任务重试管理
"""

import threading
import time
import random
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

from .logger import LoggerMixin
from .models import Job, JobStatus
from .utils import get_timestamp


class RetryStrategy(Enum):
    """重试策略"""
    IMMEDIATE = "immediate"           # 立即重试
    FIXED_DELAY = "fixed_delay"       # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避
    RANDOM_JITTER = "random_jitter"   # 随机抖动


class FailureType(Enum):
    """失败类型"""
    TIMEOUT = "timeout"               # 超时
    RESOURCE_ERROR = "resource_error" # 资源错误
    NETWORK_ERROR = "network_error"   # 网络错误
    RUNNER_ERROR = "runner_error"     # Runner错误
    EXECUTION_ERROR = "execution_error"  # 执行错误
    UNKNOWN = "unknown"               # 未知错误


@dataclass
class RetryAttempt:
    """重试尝试记录"""
    attempt_number: int
    timestamp: float
    runner_id: Optional[str]
    failure_reason: str
    failure_type: FailureType
    retry_delay: float = 0.0


@dataclass
class RetryableJob:
    """可重试任务"""
    job: Job
    max_retries: int
    current_attempts: int = 0
    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0
    max_delay: float = 300.0
    retry_attempts: List[RetryAttempt] = field(default_factory=list)
    next_retry_time: Optional[float] = None
    last_failure_type: Optional[FailureType] = None
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.current_attempts < self.max_retries
    
    def calculate_next_delay(self) -> float:
        """计算下次重试延迟"""
        if self.retry_strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        
        elif self.retry_strategy == RetryStrategy.FIXED_DELAY:
            return self.base_delay
        
        elif self.retry_strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.base_delay * (2 ** self.current_attempts)
            return min(delay, self.max_delay)
        
        elif self.retry_strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.base_delay * (self.current_attempts + 1)
            return min(delay, self.max_delay)
        
        elif self.retry_strategy == RetryStrategy.RANDOM_JITTER:
            base_delay = self.base_delay * (2 ** self.current_attempts)
            jitter = random.uniform(0.5, 1.5)
            delay = base_delay * jitter
            return min(delay, self.max_delay)
        
        else:
            return self.base_delay


class RetryManager(LoggerMixin):
    """重试管理器"""
    
    def __init__(self):
        self.running = False
        
        # 重试任务存储
        self.retryable_jobs: Dict[str, RetryableJob] = {}
        self.retry_queue = deque()  # 等待重试的任务队列
        
        # 重试统计
        self.stats = {
            'total_retries': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'abandoned_jobs': 0,
            'retries_by_type': {ft.value: 0 for ft in FailureType}
        }
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 重试线程
        self.retry_thread = None
        self.shutdown_event = threading.Event()
        
        # 回调函数
        self.retry_callbacks: List[Callable] = []
        self.abandon_callbacks: List[Callable] = []
        
        self.logger.info("重试管理器初始化完成")
    
    def start(self):
        """启动重试管理器"""
        if self.running:
            return
        
        self.logger.info("启动重试管理器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动重试处理线程
        self.retry_thread = threading.Thread(
            target=self._retry_loop,
            name="RetryManager",
            daemon=True
        )
        self.retry_thread.start()
        
        self.logger.info("重试管理器启动成功")
    
    def shutdown(self):
        """关闭重试管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭重试管理器...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.retry_thread and self.retry_thread.is_alive():
            self.retry_thread.join(timeout=10)
        
        self.logger.info("重试管理器已关闭")
    
    def register_job(self, job: Job, max_retries: int = None, 
                    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
                    base_delay: float = 1.0, max_delay: float = 300.0):
        """注册可重试任务"""
        with self.lock:
            if max_retries is None:
                max_retries = getattr(job, 'max_retries', 3)
            
            retryable_job = RetryableJob(
                job=job,
                max_retries=max_retries,
                retry_strategy=retry_strategy,
                base_delay=base_delay,
                max_delay=max_delay
            )
            
            self.retryable_jobs[job.job_id] = retryable_job
            self.logger.info(f"注册可重试任务: {job.job_id} (最大重试: {max_retries})")
    
    def handle_job_failure(self, job_id: str, failure_reason: str, 
                          failure_type: FailureType = FailureType.UNKNOWN,
                          runner_id: str = None) -> bool:
        """处理任务失败"""
        with self.lock:
            if job_id not in self.retryable_jobs:
                self.logger.warning(f"任务未注册为可重试: {job_id}")
                return False
            
            retryable_job = self.retryable_jobs[job_id]
            
            # 记录失败尝试
            attempt = RetryAttempt(
                attempt_number=retryable_job.current_attempts + 1,
                timestamp=get_timestamp(),
                runner_id=runner_id,
                failure_reason=failure_reason,
                failure_type=failure_type
            )
            
            retryable_job.retry_attempts.append(attempt)
            retryable_job.current_attempts += 1
            retryable_job.last_failure_type = failure_type
            
            # 更新统计
            self.stats['retries_by_type'][failure_type.value] += 1
            
            self.logger.warning(f"任务失败: {job_id} - {failure_reason} (尝试 {retryable_job.current_attempts}/{retryable_job.max_retries})")
            
            # 检查是否可以重试
            if retryable_job.can_retry():
                # 计算重试延迟
                delay = retryable_job.calculate_next_delay()
                retryable_job.next_retry_time = get_timestamp() + delay
                
                # 添加到重试队列
                self.retry_queue.append(job_id)
                
                self.logger.info(f"任务将在 {delay:.1f} 秒后重试: {job_id}")
                return True
            else:
                # 达到最大重试次数，放弃任务
                self._abandon_job(job_id, "达到最大重试次数")
                return False
    
    def _abandon_job(self, job_id: str, reason: str):
        """放弃任务"""
        with self.lock:
            if job_id in self.retryable_jobs:
                retryable_job = self.retryable_jobs[job_id]
                
                self.stats['abandoned_jobs'] += 1
                
                self.logger.error(f"放弃任务: {job_id} - {reason}")
                
                # 调用放弃回调
                for callback in self.abandon_callbacks:
                    try:
                        callback(retryable_job.job, reason)
                    except Exception as e:
                        self.logger.error(f"放弃任务回调异常: {e}")
                
                # 从重试任务中移除
                del self.retryable_jobs[job_id]
    
    def _retry_loop(self):
        """重试处理循环"""
        self.logger.info("重试处理循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                current_time = get_timestamp()
                ready_jobs = []
                
                with self.lock:
                    # 检查重试队列中的任务
                    while self.retry_queue:
                        job_id = self.retry_queue.popleft()
                        
                        if job_id not in self.retryable_jobs:
                            continue
                        
                        retryable_job = self.retryable_jobs[job_id]
                        
                        # 检查是否到了重试时间
                        if (retryable_job.next_retry_time and 
                            current_time >= retryable_job.next_retry_time):
                            ready_jobs.append(job_id)
                        else:
                            # 重新放回队列
                            self.retry_queue.append(job_id)
                            break
                
                # 处理准备重试的任务
                for job_id in ready_jobs:
                    self._execute_retry(job_id)
                
                # 等待下一次检查
                self.shutdown_event.wait(1)
                
            except Exception as e:
                self.logger.error(f"重试处理循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _execute_retry(self, job_id: str):
        """执行重试"""
        with self.lock:
            if job_id not in self.retryable_jobs:
                return
            
            retryable_job = self.retryable_jobs[job_id]
            
            self.stats['total_retries'] += 1
            
            self.logger.info(f"执行任务重试: {job_id} (第 {retryable_job.current_attempts} 次重试)")
            
            # 调用重试回调
            for callback in self.retry_callbacks:
                try:
                    callback(retryable_job.job, retryable_job.current_attempts)
                except Exception as e:
                    self.logger.error(f"重试回调异常: {e}")
    
    def handle_job_success(self, job_id: str):
        """处理任务成功"""
        with self.lock:
            if job_id in self.retryable_jobs:
                retryable_job = self.retryable_jobs[job_id]
                
                if retryable_job.current_attempts > 0:
                    self.stats['successful_retries'] += 1
                    self.logger.info(f"重试任务成功: {job_id} (重试 {retryable_job.current_attempts} 次)")
                
                # 从重试任务中移除
                del self.retryable_jobs[job_id]
    
    def get_retry_info(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务重试信息"""
        with self.lock:
            if job_id not in self.retryable_jobs:
                return None
            
            retryable_job = self.retryable_jobs[job_id]
            
            return {
                'job_id': job_id,
                'max_retries': retryable_job.max_retries,
                'current_attempts': retryable_job.current_attempts,
                'retry_strategy': retryable_job.retry_strategy.value,
                'next_retry_time': retryable_job.next_retry_time,
                'last_failure_type': retryable_job.last_failure_type.value if retryable_job.last_failure_type else None,
                'retry_attempts': [
                    {
                        'attempt_number': attempt.attempt_number,
                        'timestamp': attempt.timestamp,
                        'runner_id': attempt.runner_id,
                        'failure_reason': attempt.failure_reason,
                        'failure_type': attempt.failure_type.value
                    }
                    for attempt in retryable_job.retry_attempts
                ]
            }
    
    def get_all_retry_jobs(self) -> List[Dict[str, Any]]:
        """获取所有重试任务信息"""
        with self.lock:
            return [
                self.get_retry_info(job_id) 
                for job_id in self.retryable_jobs.keys()
            ]
    
    def cancel_retry(self, job_id: str) -> bool:
        """取消任务重试"""
        with self.lock:
            if job_id in self.retryable_jobs:
                self._abandon_job(job_id, "手动取消重试")
                return True
            return False
    
    def force_retry(self, job_id: str) -> bool:
        """强制立即重试"""
        with self.lock:
            if job_id not in self.retryable_jobs:
                return False
            
            retryable_job = self.retryable_jobs[job_id]
            
            if not retryable_job.can_retry():
                return False
            
            # 设置立即重试
            retryable_job.next_retry_time = get_timestamp()
            self.retry_queue.append(job_id)
            
            self.logger.info(f"强制重试任务: {job_id}")
            return True
    
    def update_retry_config(self, job_id: str, max_retries: int = None,
                           retry_strategy: RetryStrategy = None,
                           base_delay: float = None, max_delay: float = None):
        """更新重试配置"""
        with self.lock:
            if job_id not in self.retryable_jobs:
                return False
            
            retryable_job = self.retryable_jobs[job_id]
            
            if max_retries is not None:
                retryable_job.max_retries = max_retries
            if retry_strategy is not None:
                retryable_job.retry_strategy = retry_strategy
            if base_delay is not None:
                retryable_job.base_delay = base_delay
            if max_delay is not None:
                retryable_job.max_delay = max_delay
            
            self.logger.info(f"更新重试配置: {job_id}")
            return True
    
    def add_retry_callback(self, callback: Callable):
        """添加重试回调"""
        self.retry_callbacks.append(callback)
    
    def add_abandon_callback(self, callback: Callable):
        """添加放弃任务回调"""
        self.abandon_callbacks.append(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取重试统计信息"""
        with self.lock:
            return {
                'running': self.running,
                'active_retry_jobs': len(self.retryable_jobs),
                'pending_retries': len(self.retry_queue),
                **self.stats,
                'retries_by_type': dict(self.stats['retries_by_type'])
            }
    
    def clear_completed_jobs(self, max_age_hours: int = 24):
        """清理已完成的重试记录"""
        with self.lock:
            current_time = get_timestamp()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            jobs_to_remove = []
            for job_id, retryable_job in self.retryable_jobs.items():
                if (retryable_job.retry_attempts and 
                    retryable_job.retry_attempts[-1].timestamp < cutoff_time):
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self.retryable_jobs[job_id]
            
            if jobs_to_remove:
                self.logger.info(f"清理了 {len(jobs_to_remove)} 个过期重试记录")


# 全局重试管理器实例
retry_manager = RetryManager()
