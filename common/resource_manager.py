"""
集群任务管理平台 - 资源管理器
"""

import psutil
import threading
import time
import os
import signal
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from collections import deque

from .logger import LoggerMixin
from .utils import get_timestamp


@dataclass
class ResourceSnapshot:
    """资源快照"""
    timestamp: float
    cpu_percent: float
    memory_mb: int
    memory_percent: float
    disk_usage_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: List[float]


class CPUMonitor(LoggerMixin):
    """CPU监控器"""
    
    def __init__(self, max_cpu_percent: float = 80.0, check_interval: int = 5):
        self.max_cpu_percent = max_cpu_percent
        self.check_interval = check_interval
        self.running = False
        
        # CPU使用历史
        self.cpu_history = deque(maxlen=100)
        self.lock = threading.RLock()
        
        # 回调函数
        self.limit_exceeded_callbacks: List[Callable] = []
        self.usage_callbacks: List[Callable] = []
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info(f"CPU监控器初始化，限制: {max_cpu_percent}%")
    
    def start(self):
        """启动CPU监控"""
        if self.running:
            return
        
        self.logger.info("启动CPU监控...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="CPUMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("CPU监控启动成功")
    
    def shutdown(self):
        """关闭CPU监控"""
        if not self.running:
            return
        
        self.logger.info("关闭CPU监控...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("CPU监控已关闭")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("CPU监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 获取CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                timestamp = get_timestamp()
                
                with self.lock:
                    self.cpu_history.append((timestamp, cpu_percent))
                
                # 检查是否超限
                if cpu_percent > self.max_cpu_percent:
                    self.logger.warning(f"CPU使用率超限: {cpu_percent:.1f}% > {self.max_cpu_percent}%")
                    
                    # 调用超限回调
                    for callback in self.limit_exceeded_callbacks:
                        try:
                            callback(cpu_percent, self.max_cpu_percent)
                        except Exception as e:
                            self.logger.error(f"CPU超限回调异常: {e}")
                
                # 调用使用率回调
                for callback in self.usage_callbacks:
                    try:
                        callback(cpu_percent)
                    except Exception as e:
                        self.logger.error(f"CPU使用率回调异常: {e}")
                
                # 等待下一次检查
                self.shutdown_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"CPU监控循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def get_current_usage(self) -> float:
        """获取当前CPU使用率"""
        return psutil.cpu_percent()
    
    def get_average_usage(self, minutes: int = 5) -> float:
        """获取平均CPU使用率"""
        with self.lock:
            if not self.cpu_history:
                return 0.0
            
            cutoff_time = get_timestamp() - (minutes * 60)
            recent_data = [usage for timestamp, usage in self.cpu_history 
                          if timestamp >= cutoff_time]
            
            return sum(recent_data) / len(recent_data) if recent_data else 0.0
    
    def is_overloaded(self) -> bool:
        """检查CPU是否过载"""
        return self.get_current_usage() > self.max_cpu_percent
    
    def add_limit_exceeded_callback(self, callback: Callable):
        """添加超限回调"""
        self.limit_exceeded_callbacks.append(callback)
    
    def add_usage_callback(self, callback: Callable):
        """添加使用率回调"""
        self.usage_callbacks.append(callback)


class MemoryMonitor(LoggerMixin):
    """内存监控器"""
    
    def __init__(self, max_memory_mb: int = 1024, check_interval: int = 5):
        self.max_memory_mb = max_memory_mb
        self.check_interval = check_interval
        self.running = False
        
        # 内存使用历史
        self.memory_history = deque(maxlen=100)
        self.lock = threading.RLock()
        
        # 回调函数
        self.limit_exceeded_callbacks: List[Callable] = []
        self.usage_callbacks: List[Callable] = []
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info(f"内存监控器初始化，限制: {max_memory_mb}MB")
    
    def start(self):
        """启动内存监控"""
        if self.running:
            return
        
        self.logger.info("启动内存监控...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="MemoryMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("内存监控启动成功")
    
    def shutdown(self):
        """关闭内存监控"""
        if not self.running:
            return
        
        self.logger.info("关闭内存监控...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("内存监控已关闭")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("内存监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 获取内存使用情况
                memory = psutil.virtual_memory()
                memory_mb = memory.used // (1024 * 1024)
                timestamp = get_timestamp()
                
                with self.lock:
                    self.memory_history.append((timestamp, memory_mb, memory.percent))
                
                # 检查是否超限
                if memory_mb > self.max_memory_mb:
                    self.logger.warning(f"内存使用量超限: {memory_mb}MB > {self.max_memory_mb}MB")
                    
                    # 调用超限回调
                    for callback in self.limit_exceeded_callbacks:
                        try:
                            callback(memory_mb, self.max_memory_mb)
                        except Exception as e:
                            self.logger.error(f"内存超限回调异常: {e}")
                
                # 调用使用量回调
                for callback in self.usage_callbacks:
                    try:
                        callback(memory_mb, memory.percent)
                    except Exception as e:
                        self.logger.error(f"内存使用量回调异常: {e}")
                
                # 等待下一次检查
                self.shutdown_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"内存监控循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def get_current_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            'used_mb': memory.used // (1024 * 1024),
            'percent': memory.percent,
            'available_mb': memory.available // (1024 * 1024),
            'total_mb': memory.total // (1024 * 1024)
        }
    
    def is_overloaded(self) -> bool:
        """检查内存是否过载"""
        current = self.get_current_usage()
        return current['used_mb'] > self.max_memory_mb
    
    def add_limit_exceeded_callback(self, callback: Callable):
        """添加超限回调"""
        self.limit_exceeded_callbacks.append(callback)
    
    def add_usage_callback(self, callback: Callable):
        """添加使用量回调"""
        self.usage_callbacks.append(callback)


class ProcessLimiter(LoggerMixin):
    """进程限制器"""
    
    def __init__(self, max_runtime_seconds: int = 3600):
        self.max_runtime_seconds = max_runtime_seconds
        self.running = False
        
        # 进程跟踪
        self.tracked_processes: Dict[int, Dict] = {}
        self.lock = threading.RLock()
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info(f"进程限制器初始化，最大运行时间: {max_runtime_seconds}秒")
    
    def start(self):
        """启动进程限制器"""
        if self.running:
            return
        
        self.logger.info("启动进程限制器...")
        
        self.running = True
        self.shutdown_event.clear()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="ProcessLimiter",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("进程限制器启动成功")
    
    def shutdown(self):
        """关闭进程限制器"""
        if not self.running:
            return
        
        self.logger.info("关闭进程限制器...")
        
        self.running = False
        self.shutdown_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("进程限制器已关闭")
    
    def track_process(self, pid: int, job_id: str = None):
        """跟踪进程"""
        with self.lock:
            self.tracked_processes[pid] = {
                'job_id': job_id,
                'start_time': get_timestamp(),
                'last_check': get_timestamp()
            }
            
        self.logger.info(f"开始跟踪进程: PID {pid}")
    
    def untrack_process(self, pid: int):
        """停止跟踪进程"""
        with self.lock:
            if pid in self.tracked_processes:
                del self.tracked_processes[pid]
                self.logger.info(f"停止跟踪进程: PID {pid}")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("进程限制监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                current_time = get_timestamp()
                processes_to_kill = []
                
                with self.lock:
                    for pid, info in list(self.tracked_processes.items()):
                        try:
                            # 检查进程是否还存在
                            if not psutil.pid_exists(pid):
                                del self.tracked_processes[pid]
                                continue
                            
                            # 检查运行时间
                            runtime = current_time - info['start_time']
                            if runtime > self.max_runtime_seconds:
                                processes_to_kill.append((pid, info))
                            
                            info['last_check'] = current_time
                            
                        except Exception as e:
                            self.logger.error(f"检查进程 {pid} 失败: {e}")
                
                # 终止超时进程
                for pid, info in processes_to_kill:
                    self._terminate_process(pid, info)
                
                # 等待下一次检查
                self.shutdown_event.wait(10)
                
            except Exception as e:
                self.logger.error(f"进程限制监控循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _terminate_process(self, pid: int, info: Dict):
        """终止进程"""
        try:
            job_id = info.get('job_id', 'unknown')
            runtime = get_timestamp() - info['start_time']
            
            self.logger.warning(f"终止超时进程: PID {pid}, Job {job_id}, 运行时间 {runtime:.1f}秒")
            
            # 尝试优雅终止
            try:
                os.kill(pid, signal.SIGTERM)
                time.sleep(5)
                
                # 检查是否还在运行
                if psutil.pid_exists(pid):
                    # 强制终止
                    os.kill(pid, signal.SIGKILL)
                    self.logger.warning(f"强制终止进程: PID {pid}")
                
            except ProcessLookupError:
                # 进程已经不存在
                pass
            except Exception as e:
                self.logger.error(f"终止进程 {pid} 失败: {e}")
            
            # 从跟踪列表中移除
            with self.lock:
                self.tracked_processes.pop(pid, None)
                
        except Exception as e:
            self.logger.error(f"终止进程异常: {e}")


class ResourceManager(LoggerMixin):
    """资源管理器"""
    
    def __init__(self, max_cpu_percent: float = 80.0, max_memory_mb: int = 1024,
                 max_runtime_seconds: int = 3600):
        self.running = False
        
        # 初始化各个监控器
        self.cpu_monitor = CPUMonitor(max_cpu_percent)
        self.memory_monitor = MemoryMonitor(max_memory_mb)
        self.process_limiter = ProcessLimiter(max_runtime_seconds)
        
        # 资源快照历史
        self.snapshots = deque(maxlen=1000)
        self.lock = threading.RLock()
        
        # 快照线程
        self.snapshot_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info("资源管理器初始化完成")
    
    def start(self):
        """启动资源管理器"""
        if self.running:
            return
        
        self.logger.info("启动资源管理器...")
        
        # 启动各个监控器
        self.cpu_monitor.start()
        self.memory_monitor.start()
        self.process_limiter.start()
        
        # 启动快照线程
        self.running = True
        self.shutdown_event.clear()
        
        self.snapshot_thread = threading.Thread(
            target=self._snapshot_loop,
            name="ResourceSnapshot",
            daemon=True
        )
        self.snapshot_thread.start()
        
        self.logger.info("资源管理器启动成功")
    
    def shutdown(self):
        """关闭资源管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭资源管理器...")
        
        self.running = False
        self.shutdown_event.set()
        
        # 关闭各个监控器
        self.cpu_monitor.shutdown()
        self.memory_monitor.shutdown()
        self.process_limiter.shutdown()
        
        # 等待快照线程结束
        if self.snapshot_thread and self.snapshot_thread.is_alive():
            self.snapshot_thread.join(timeout=5)
        
        self.logger.info("资源管理器已关闭")
    
    def _snapshot_loop(self):
        """资源快照循环"""
        while not self.shutdown_event.is_set():
            try:
                # 创建资源快照
                snapshot = self._create_snapshot()
                
                with self.lock:
                    self.snapshots.append(snapshot)
                
                # 等待下一次快照
                self.shutdown_event.wait(30)
                
            except Exception as e:
                self.logger.error(f"资源快照循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def _create_snapshot(self) -> ResourceSnapshot:
        """创建资源快照"""
        try:
            # 获取系统信息
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            return ResourceSnapshot(
                timestamp=get_timestamp(),
                cpu_percent=cpu_percent,
                memory_mb=memory.used // (1024 * 1024),
                memory_percent=memory.percent,
                disk_usage_percent=disk.percent,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                process_count=len(psutil.pids()),
                load_average=psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            )
        except Exception as e:
            self.logger.error(f"创建资源快照失败: {e}")
            return ResourceSnapshot(
                timestamp=get_timestamp(),
                cpu_percent=0, memory_mb=0, memory_percent=0,
                disk_usage_percent=0, network_bytes_sent=0,
                network_bytes_recv=0, process_count=0, load_average=[0, 0, 0]
            )
    
    def get_current_status(self) -> Dict:
        """获取当前资源状态"""
        return {
            'cpu': self.cpu_monitor.get_current_usage(),
            'memory': self.memory_monitor.get_current_usage(),
            'cpu_overloaded': self.cpu_monitor.is_overloaded(),
            'memory_overloaded': self.memory_monitor.is_overloaded(),
            'tracked_processes': len(self.process_limiter.tracked_processes)
        }
    
    def track_job_process(self, pid: int, job_id: str):
        """跟踪任务进程"""
        self.process_limiter.track_process(pid, job_id)
    
    def untrack_job_process(self, pid: int):
        """停止跟踪任务进程"""
        self.process_limiter.untrack_process(pid)
