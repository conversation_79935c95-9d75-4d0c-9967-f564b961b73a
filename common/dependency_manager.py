"""
集群任务管理平台 - 任务依赖管理
"""

import threading
from typing import Dict, List, Set, Optional, Tuple, Callable
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum

from .logger import LoggerMixin
from .models import Job, JobStatus
from .utils import get_timestamp


class DependencyType(Enum):
    """依赖类型"""
    SEQUENTIAL = "sequential"    # 顺序依赖
    PARALLEL = "parallel"        # 并行依赖
    CONDITIONAL = "conditional"  # 条件依赖


@dataclass
class Dependency:
    """依赖关系"""
    job_id: str
    depends_on: str
    dependency_type: DependencyType = DependencyType.SEQUENTIAL
    condition: Optional[Callable] = None  # 条件依赖的判断函数


class DependencyGraph(LoggerMixin):
    """依赖图"""
    
    def __init__(self):
        # 依赖关系存储
        self.dependencies: Dict[str, List[Dependency]] = defaultdict(list)  # job_id -> dependencies
        self.dependents: Dict[str, Set[str]] = defaultdict(set)  # job_id -> dependent jobs
        
        # 任务状态
        self.job_statuses: Dict[str, JobStatus] = {}
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        self.logger.info("依赖图初始化完成")
    
    def add_dependency(self, job_id: str, depends_on: str, 
                      dependency_type: DependencyType = DependencyType.SEQUENTIAL,
                      condition: Callable = None):
        """添加依赖关系"""
        with self.lock:
            # 检查循环依赖
            if self._would_create_cycle(job_id, depends_on):
                raise ValueError(f"添加依赖会创建循环: {job_id} -> {depends_on}")
            
            dependency = Dependency(
                job_id=job_id,
                depends_on=depends_on,
                dependency_type=dependency_type,
                condition=condition
            )
            
            self.dependencies[job_id].append(dependency)
            self.dependents[depends_on].add(job_id)
            
            self.logger.info(f"添加依赖关系: {job_id} -> {depends_on} ({dependency_type.value})")
    
    def remove_dependency(self, job_id: str, depends_on: str):
        """移除依赖关系"""
        with self.lock:
            # 从依赖列表中移除
            self.dependencies[job_id] = [
                dep for dep in self.dependencies[job_id] 
                if dep.depends_on != depends_on
            ]
            
            # 从被依赖列表中移除
            self.dependents[depends_on].discard(job_id)
            
            self.logger.info(f"移除依赖关系: {job_id} -> {depends_on}")
    
    def _would_create_cycle(self, job_id: str, depends_on: str) -> bool:
        """检查是否会创建循环依赖"""
        visited = set()
        
        def dfs(current: str) -> bool:
            if current == job_id:
                return True
            if current in visited:
                return False
            
            visited.add(current)
            
            for dep in self.dependencies.get(current, []):
                if dfs(dep.depends_on):
                    return True
            
            return False
        
        return dfs(depends_on)
    
    def get_dependencies(self, job_id: str) -> List[Dependency]:
        """获取任务的依赖"""
        with self.lock:
            return self.dependencies.get(job_id, []).copy()
    
    def get_dependents(self, job_id: str) -> Set[str]:
        """获取依赖此任务的任务列表"""
        with self.lock:
            return self.dependents.get(job_id, set()).copy()
    
    def update_job_status(self, job_id: str, status: JobStatus):
        """更新任务状态"""
        with self.lock:
            old_status = self.job_statuses.get(job_id)
            self.job_statuses[job_id] = status
            
            self.logger.debug(f"任务状态更新: {job_id} - {status.value}")
            
            # 如果任务完成，检查依赖此任务的其他任务
            if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                self._notify_dependents(job_id, status)
    
    def _notify_dependents(self, completed_job_id: str, status: JobStatus):
        """通知依赖任务"""
        dependents = self.get_dependents(completed_job_id)
        
        for dependent_job_id in dependents:
            self.logger.debug(f"通知依赖任务: {dependent_job_id} (依赖 {completed_job_id} 已{status.value})")
    
    def can_execute(self, job_id: str) -> Tuple[bool, List[str]]:
        """检查任务是否可以执行"""
        with self.lock:
            dependencies = self.get_dependencies(job_id)
            
            if not dependencies:
                return True, []
            
            blocking_dependencies = []
            
            for dep in dependencies:
                depends_on_status = self.job_statuses.get(dep.depends_on, JobStatus.PENDING)
                
                if dep.dependency_type == DependencyType.SEQUENTIAL:
                    # 顺序依赖：必须等待前置任务完成
                    if depends_on_status != JobStatus.COMPLETED:
                        blocking_dependencies.append(dep.depends_on)
                
                elif dep.dependency_type == DependencyType.PARALLEL:
                    # 并行依赖：前置任务必须已开始
                    if depends_on_status == JobStatus.PENDING:
                        blocking_dependencies.append(dep.depends_on)
                
                elif dep.dependency_type == DependencyType.CONDITIONAL:
                    # 条件依赖：检查条件
                    if dep.condition and not dep.condition(depends_on_status):
                        blocking_dependencies.append(dep.depends_on)
            
            can_run = len(blocking_dependencies) == 0
            return can_run, blocking_dependencies
    
    def get_ready_jobs(self, pending_jobs: List[str]) -> List[str]:
        """获取可以执行的任务列表"""
        ready_jobs = []
        
        for job_id in pending_jobs:
            can_run, _ = self.can_execute(job_id)
            if can_run:
                ready_jobs.append(job_id)
        
        return ready_jobs
    
    def topological_sort(self, job_ids: List[str]) -> List[str]:
        """拓扑排序"""
        with self.lock:
            # 计算入度
            in_degree = defaultdict(int)
            graph = defaultdict(list)
            
            for job_id in job_ids:
                in_degree[job_id] = 0
            
            for job_id in job_ids:
                for dep in self.dependencies.get(job_id, []):
                    if dep.depends_on in job_ids:
                        graph[dep.depends_on].append(job_id)
                        in_degree[job_id] += 1
            
            # 拓扑排序
            queue = deque([job_id for job_id in job_ids if in_degree[job_id] == 0])
            result = []
            
            while queue:
                current = queue.popleft()
                result.append(current)
                
                for neighbor in graph[current]:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        queue.append(neighbor)
            
            if len(result) != len(job_ids):
                raise ValueError("存在循环依赖，无法进行拓扑排序")
            
            return result
    
    def get_execution_plan(self, job_ids: List[str]) -> List[List[str]]:
        """获取执行计划（分层执行）"""
        with self.lock:
            sorted_jobs = self.topological_sort(job_ids)
            execution_plan = []
            
            remaining_jobs = set(sorted_jobs)
            
            while remaining_jobs:
                current_batch = []
                
                # 找到当前可以执行的任务
                for job_id in list(remaining_jobs):
                    can_run, blocking = self.can_execute(job_id)
                    
                    # 检查阻塞的依赖是否都已在之前的批次中
                    if can_run or all(dep not in remaining_jobs for dep in blocking):
                        current_batch.append(job_id)
                
                if not current_batch:
                    # 如果没有可执行的任务，说明存在问题
                    raise ValueError(f"无法继续执行，剩余任务: {remaining_jobs}")
                
                execution_plan.append(current_batch)
                remaining_jobs -= set(current_batch)
            
            return execution_plan
    
    def clear_job(self, job_id: str):
        """清理任务的所有依赖关系"""
        with self.lock:
            # 移除此任务的所有依赖
            if job_id in self.dependencies:
                for dep in self.dependencies[job_id]:
                    self.dependents[dep.depends_on].discard(job_id)
                del self.dependencies[job_id]
            
            # 移除依赖此任务的关系
            if job_id in self.dependents:
                for dependent in self.dependents[job_id]:
                    self.dependencies[dependent] = [
                        dep for dep in self.dependencies[dependent]
                        if dep.depends_on != job_id
                    ]
                del self.dependents[job_id]
            
            # 移除状态
            self.job_statuses.pop(job_id, None)
            
            self.logger.info(f"清理任务依赖关系: {job_id}")
    
    def get_stats(self) -> Dict:
        """获取依赖图统计信息"""
        with self.lock:
            total_dependencies = sum(len(deps) for deps in self.dependencies.values())
            
            return {
                'total_jobs': len(self.job_statuses),
                'total_dependencies': total_dependencies,
                'jobs_with_dependencies': len([j for j in self.dependencies.values() if j]),
                'jobs_being_depended_on': len([d for d in self.dependents.values() if d])
            }


class DependencyManager(LoggerMixin):
    """依赖管理器"""
    
    def __init__(self):
        self.dependency_graph = DependencyGraph()
        self.running = False
        
        # 回调函数
        self.ready_job_callbacks: List[Callable] = []
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        self.logger.info("依赖管理器初始化完成")
    
    def start(self):
        """启动依赖管理器"""
        if self.running:
            return
        
        self.logger.info("启动依赖管理器...")
        self.running = True
        self.logger.info("依赖管理器启动成功")
    
    def shutdown(self):
        """关闭依赖管理器"""
        if not self.running:
            return
        
        self.logger.info("关闭依赖管理器...")
        self.running = False
        self.logger.info("依赖管理器已关闭")
    
    def add_job_dependency(self, job: Job):
        """添加任务的依赖关系"""
        if not job.dependencies:
            return
        
        with self.lock:
            for dep_job_id in job.dependencies:
                self.dependency_graph.add_dependency(
                    job.job_id, 
                    dep_job_id, 
                    DependencyType.SEQUENTIAL
                )
            
            self.logger.info(f"添加任务依赖: {job.job_id} -> {job.dependencies}")
    
    def remove_job_dependency(self, job_id: str, depends_on: str):
        """移除任务依赖"""
        with self.lock:
            self.dependency_graph.remove_dependency(job_id, depends_on)
    
    def update_job_status(self, job_id: str, status: JobStatus):
        """更新任务状态"""
        with self.lock:
            self.dependency_graph.update_job_status(job_id, status)
            
            # 如果任务完成，检查是否有新的可执行任务
            if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                self._check_ready_jobs()
    
    def _check_ready_jobs(self):
        """检查是否有新的可执行任务"""
        # 获取所有待执行的任务
        pending_jobs = [
            job_id for job_id, status in self.dependency_graph.job_statuses.items()
            if status == JobStatus.PENDING
        ]
        
        # 获取可执行的任务
        ready_jobs = self.dependency_graph.get_ready_jobs(pending_jobs)
        
        # 通知回调
        for job_id in ready_jobs:
            for callback in self.ready_job_callbacks:
                try:
                    callback(job_id)
                except Exception as e:
                    self.logger.error(f"就绪任务回调异常: {e}")
    
    def can_execute_job(self, job_id: str) -> Tuple[bool, List[str]]:
        """检查任务是否可以执行"""
        with self.lock:
            return self.dependency_graph.can_execute(job_id)
    
    def get_execution_plan(self, job_ids: List[str]) -> List[List[str]]:
        """获取任务执行计划"""
        with self.lock:
            return self.dependency_graph.get_execution_plan(job_ids)
    
    def get_ready_jobs(self, pending_jobs: List[str]) -> List[str]:
        """获取可以执行的任务"""
        with self.lock:
            return self.dependency_graph.get_ready_jobs(pending_jobs)
    
    def clear_job(self, job_id: str):
        """清理任务"""
        with self.lock:
            self.dependency_graph.clear_job(job_id)
    
    def add_ready_job_callback(self, callback: Callable):
        """添加就绪任务回调"""
        self.ready_job_callbacks.append(callback)
    
    def get_dependency_info(self, job_id: str) -> Dict:
        """获取任务依赖信息"""
        with self.lock:
            dependencies = self.dependency_graph.get_dependencies(job_id)
            dependents = self.dependency_graph.get_dependents(job_id)
            can_run, blocking = self.dependency_graph.can_execute(job_id)
            
            return {
                'job_id': job_id,
                'dependencies': [
                    {
                        'depends_on': dep.depends_on,
                        'type': dep.dependency_type.value,
                        'status': self.dependency_graph.job_statuses.get(dep.depends_on, 'unknown')
                    }
                    for dep in dependencies
                ],
                'dependents': list(dependents),
                'can_execute': can_run,
                'blocking_dependencies': blocking
            }
    
    def get_stats(self) -> Dict:
        """获取依赖管理器统计信息"""
        with self.lock:
            return {
                'running': self.running,
                'dependency_graph': self.dependency_graph.get_stats(),
                'ready_job_callbacks': len(self.ready_job_callbacks)
            }


# 全局依赖管理器实例
dependency_manager = DependencyManager()
