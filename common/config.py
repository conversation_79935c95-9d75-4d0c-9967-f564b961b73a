"""
集群任务管理平台 - 配置管理模块
"""

import json
import os
from typing import Any, Dict, Optional, Union
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self._config = {}
        self._config_path = config_path
        self._load_default_config()
        
        if config_path:
            self.load_config(config_path)
    
    def _load_default_config(self):
        """加载默认配置"""
        self._config = {
            "manager": {
                "host": "localhost",
                "port": 8080,
                "max_workers": 10,
                "heartbeat_timeout": 60,
                "job_cleanup_interval": 300
            },
            "runner": {
                "heartbeat_interval": 30,
                "max_concurrent_jobs": 3,
                "resource_check_interval": 10,
                "default_limits": {
                    "max_cpu_percent": 80.0,
                    "max_memory_mb": 1024,
                    "max_runtime_seconds": 3600
                }
            },
            "job": {
                "default_timeout": 1800,
                "max_retries": 3,
                "retry_delay": 60
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/cluster.log",
                "max_size": "10MB",
                "backup_count": 5
            },
            "database": {
                "type": "sqlite",
                "path": "data/cluster.db"
            }
        }
    
    def load_config(self, config_path: str):
        """从文件加载配置"""
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 深度合并配置
            self._deep_merge(self._config, file_config)
            self._config_path = config_path
            
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def _deep_merge(self, base: Dict, update: Dict):
        """深度合并字典"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self._config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置最终值
        config[keys[-1]] = value
    
    def save(self, config_path: Optional[str] = None):
        """保存配置到文件"""
        path = config_path or self._config_path
        if not path:
            raise ValueError("未指定配置文件路径")
        
        # 确保目录存在
        config_file = Path(path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self._config, f, indent=2, ensure_ascii=False)
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置段"""
        return self.get(section, {})
    
    def update_section(self, section: str, updates: Dict[str, Any]):
        """更新配置段"""
        current = self.get_section(section)
        self._deep_merge(current, updates)
        self.set(section, current)
    
    def to_dict(self) -> Dict[str, Any]:
        """返回完整配置字典"""
        return self._config.copy()
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证必需的配置项
            required_keys = [
                'manager.host',
                'manager.port',
                'runner.heartbeat_interval',
                'logging.level'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    raise ValueError(f"缺少必需的配置项: {key}")
            
            # 验证端口号
            port = self.get('manager.port')
            if not isinstance(port, int) or port < 1 or port > 65535:
                raise ValueError(f"无效的端口号: {port}")
            
            # 验证心跳间隔
            heartbeat = self.get('runner.heartbeat_interval')
            if not isinstance(heartbeat, (int, float)) or heartbeat <= 0:
                raise ValueError(f"无效的心跳间隔: {heartbeat}")
            
            return True
            
        except Exception as e:
            raise ValueError(f"配置验证失败: {e}")


# 全局配置实例
_global_config = None


def get_config(config_path: Optional[str] = None) -> ConfigManager:
    """获取全局配置实例"""
    global _global_config
    
    if _global_config is None:
        # 尝试从默认位置加载配置
        default_paths = [
            'config/local.json',
            'config/default.json',
            os.path.expanduser('~/.cluster/config.json')
        ]
        
        config_file = config_path
        if not config_file:
            for path in default_paths:
                if os.path.exists(path):
                    config_file = path
                    break
        
        _global_config = ConfigManager(config_file)
    
    return _global_config


def reload_config(config_path: Optional[str] = None):
    """重新加载配置"""
    global _global_config
    _global_config = None
    return get_config(config_path)


# 便捷函数
def get_manager_config() -> Dict[str, Any]:
    """获取管理程序配置"""
    return get_config().get_section('manager')


def get_runner_config() -> Dict[str, Any]:
    """获取Runner配置"""
    return get_config().get_section('runner')


def get_job_config() -> Dict[str, Any]:
    """获取任务配置"""
    return get_config().get_section('job')


def get_logging_config() -> Dict[str, Any]:
    """获取日志配置"""
    return get_config().get_section('logging')


# 示例使用
if __name__ == "__main__":
    # 创建配置管理器
    config = ConfigManager()
    
    # 获取配置值
    print("Manager host:", config.get('manager.host'))
    print("Manager port:", config.get('manager.port'))
    
    # 设置配置值
    config.set('manager.host', '0.0.0.0')
    print("Updated host:", config.get('manager.host'))
    
    # 验证配置
    try:
        config.validate()
        print("配置验证通过")
    except ValueError as e:
        print(f"配置验证失败: {e}")
