"""
集群任务管理平台 - 日志工具模块
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional
import json


class ClusterLogger:
    """集群日志管理器"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, name: str, config: Optional[dict] = None) -> logging.Logger:
        """获取或创建日志器"""
        if name in cls._loggers:
            return cls._loggers[name]
        
        logger = logging.getLogger(name)
        
        # 避免重复添加处理器
        if logger.handlers:
            cls._loggers[name] = logger
            return logger
        
        # 默认配置
        default_config = {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": None,
            "max_size": "10MB",
            "backup_count": 5
        }
        
        if config:
            default_config.update(config)
        
        # 设置日志级别
        level = getattr(logging, default_config["level"].upper())
        logger.setLevel(level)
        
        # 创建格式器
        formatter = logging.Formatter(default_config["format"])
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if default_config["file"]:
            # 确保日志目录存在
            log_dir = os.path.dirname(default_config["file"])
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            # 解析文件大小
            max_bytes = cls._parse_size(default_config["max_size"])
            
            file_handler = logging.handlers.RotatingFileHandler(
                default_config["file"],
                maxBytes=max_bytes,
                backupCount=default_config["backup_count"],
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        cls._loggers[name] = logger
        return logger
    
    @staticmethod
    def _parse_size(size_str: str) -> int:
        """解析大小字符串，如 '10MB' -> 10485760"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    @classmethod
    def setup_from_config(cls, config_path: str):
        """从配置文件设置日志"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            logging_config = config.get('logging', {})
            
            # 设置根日志器
            root_logger = cls.get_logger('cluster', logging_config)
            
            return root_logger
        except Exception as e:
            # 如果配置文件有问题，使用默认配置
            logger = cls.get_logger('cluster')
            logger.warning(f"无法加载日志配置文件 {config_path}: {e}")
            return logger


def get_logger(name: str = None) -> logging.Logger:
    """便捷函数：获取日志器"""
    if name is None:
        name = 'cluster'
    return ClusterLogger.get_logger(name)


# 预定义的日志器
def get_manager_logger() -> logging.Logger:
    """获取管理程序日志器"""
    return ClusterLogger.get_logger('cluster.manager')


def get_runner_logger() -> logging.Logger:
    """获取Runner日志器"""
    return ClusterLogger.get_logger('cluster.runner')


def get_job_logger() -> logging.Logger:
    """获取任务日志器"""
    return ClusterLogger.get_logger('cluster.job')


def get_api_logger() -> logging.Logger:
    """获取API日志器"""
    return ClusterLogger.get_logger('cluster.api')


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        class_name = self.__class__.__name__
        module_name = self.__class__.__module__
        logger_name = f"cluster.{module_name.split('.')[-1]}.{class_name}"
        return ClusterLogger.get_logger(logger_name)


# 示例使用
if __name__ == "__main__":
    # 基本使用
    logger = get_logger()
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    # 使用混入类
    class TestClass(LoggerMixin):
        def test_method(self):
            self.logger.info("测试方法执行")
    
    test = TestClass()
    test.test_method()
