"""
集群任务管理平台 - 网络通信协议
"""

import socket
import threading
import time
import json
import struct
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from .logger import LoggerMixin
from .utils import get_timestamp, retry_on_exception


class MessageType(Enum):
    """消息类型"""
    HEARTBEAT = "heartbeat"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"


@dataclass
class NetworkMessage:
    """网络消息"""
    message_type: MessageType
    message_id: str
    sender_id: str
    receiver_id: str
    data: Dict[str, Any]
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = get_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'message_type': self.message_type.value,
            'message_id': self.message_id,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'data': self.data,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NetworkMessage':
        """从字典创建"""
        return cls(
            message_type=MessageType(data['message_type']),
            message_id=data['message_id'],
            sender_id=data['sender_id'],
            receiver_id=data['receiver_id'],
            data=data['data'],
            timestamp=data['timestamp']
        )


class NetworkProtocol(LoggerMixin):
    """网络协议基类"""
    
    def __init__(self, node_id: str):
        self.node_id = node_id
        self.running = False
        self.connections: Dict[str, socket.socket] = {}
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.lock = threading.RLock()
        
        # 协议配置
        self.buffer_size = 8192
        self.timeout = 30
        self.heartbeat_interval = 30
        self.max_retries = 3
        
        self.logger.info(f"网络协议初始化: {node_id}")
    
    def register_handler(self, message_type: MessageType, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
        self.logger.debug(f"注册消息处理器: {message_type.value}")
    
    def send_message(self, target_id: str, message: NetworkMessage) -> bool:
        """发送消息"""
        try:
            # 序列化消息
            data = json.dumps(message.to_dict()).encode('utf-8')
            
            # 添加长度前缀
            length = struct.pack('!I', len(data))
            full_data = length + data
            
            # 获取连接
            conn = self._get_connection(target_id)
            if not conn:
                self.logger.error(f"无法获取到 {target_id} 的连接")
                return False
            
            # 发送数据
            conn.sendall(full_data)
            self.logger.debug(f"消息发送成功: {message.message_id} -> {target_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            self._close_connection(target_id)
            return False
    
    def _get_connection(self, target_id: str) -> Optional[socket.socket]:
        """获取连接"""
        with self.lock:
            return self.connections.get(target_id)
    
    def _close_connection(self, target_id: str):
        """关闭连接"""
        with self.lock:
            if target_id in self.connections:
                try:
                    self.connections[target_id].close()
                except:
                    pass
                del self.connections[target_id]
                self.logger.info(f"连接已关闭: {target_id}")
    
    def _receive_message(self, conn: socket.socket) -> Optional[NetworkMessage]:
        """接收消息"""
        try:
            # 接收长度前缀
            length_data = self._recv_exact(conn, 4)
            if not length_data:
                return None
            
            length = struct.unpack('!I', length_data)[0]
            
            # 接收消息数据
            message_data = self._recv_exact(conn, length)
            if not message_data:
                return None
            
            # 反序列化消息
            data = json.loads(message_data.decode('utf-8'))
            message = NetworkMessage.from_dict(data)
            
            self.logger.debug(f"消息接收成功: {message.message_id}")
            return message
            
        except Exception as e:
            self.logger.error(f"接收消息失败: {e}")
            return None
    
    def _recv_exact(self, conn: socket.socket, length: int) -> Optional[bytes]:
        """精确接收指定长度的数据"""
        data = b''
        while len(data) < length:
            try:
                chunk = conn.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.timeout:
                return None
            except Exception:
                return None
        return data
    
    def _handle_message(self, message: NetworkMessage):
        """处理消息"""
        try:
            handler = self.message_handlers.get(message.message_type)
            if handler:
                handler(message)
            else:
                self.logger.warning(f"未找到消息处理器: {message.message_type.value}")
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")


class TCPServer(NetworkProtocol):
    """TCP服务器"""
    
    def __init__(self, node_id: str, host: str = 'localhost', port: int = 0):
        super().__init__(node_id)
        self.host = host
        self.port = port
        self.server_socket = None
        self.client_threads: List[threading.Thread] = []
        
    def start(self) -> int:
        """启动服务器，返回实际端口"""
        if self.running:
            return self.port
        
        try:
            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            
            # 获取实际端口
            self.port = self.server_socket.getsockname()[1]
            
            self.server_socket.listen(5)
            self.running = True
            
            # 启动接受连接的线程
            accept_thread = threading.Thread(
                target=self._accept_connections,
                name=f"TCPServer-{self.node_id}",
                daemon=True
            )
            accept_thread.start()
            
            self.logger.info(f"TCP服务器启动: {self.host}:{self.port}")
            return self.port
            
        except Exception as e:
            self.logger.error(f"启动TCP服务器失败: {e}")
            raise
    
    def shutdown(self):
        """关闭服务器"""
        if not self.running:
            return
        
        self.logger.info("关闭TCP服务器...")
        self.running = False
        
        # 关闭服务器套接字
        if self.server_socket:
            self.server_socket.close()
        
        # 关闭所有客户端连接
        with self.lock:
            for conn in list(self.connections.values()):
                try:
                    conn.close()
                except:
                    pass
            self.connections.clear()
        
        # 等待客户端线程结束
        for thread in self.client_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.logger.info("TCP服务器已关闭")
    
    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                conn, addr = self.server_socket.accept()
                conn.settimeout(self.timeout)
                
                self.logger.info(f"新客户端连接: {addr}")
                
                # 为每个客户端创建处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(conn, addr),
                    name=f"Client-{addr[0]}:{addr[1]}",
                    daemon=True
                )
                client_thread.start()
                self.client_threads.append(client_thread)
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"接受连接失败: {e}")
                break
    
    def _handle_client(self, conn: socket.socket, addr):
        """处理客户端连接"""
        client_id = f"{addr[0]}:{addr[1]}"
        
        try:
            # 注册连接
            with self.lock:
                self.connections[client_id] = conn
            
            # 处理消息
            while self.running:
                message = self._receive_message(conn)
                if not message:
                    break
                
                # 更新连接映射
                if message.sender_id != client_id:
                    with self.lock:
                        if client_id in self.connections:
                            del self.connections[client_id]
                        self.connections[message.sender_id] = conn
                    client_id = message.sender_id
                
                # 处理消息
                self._handle_message(message)
                
        except Exception as e:
            self.logger.error(f"处理客户端连接失败: {e}")
        finally:
            # 清理连接
            self._close_connection(client_id)
            try:
                conn.close()
            except:
                pass


class TCPClient(NetworkProtocol):
    """TCP客户端"""
    
    def __init__(self, node_id: str):
        super().__init__(node_id)
        self.receive_thread = None
    
    @retry_on_exception(max_retries=3, delay=1.0)
    def connect(self, target_id: str, host: str, port: int) -> bool:
        """连接到服务器"""
        try:
            # 创建套接字
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((host, port))
            
            # 注册连接
            with self.lock:
                self.connections[target_id] = sock
            
            # 启动接收线程
            if not self.receive_thread or not self.receive_thread.is_alive():
                self.receive_thread = threading.Thread(
                    target=self._receive_loop,
                    args=(target_id, sock),
                    name=f"TCPClient-{self.node_id}",
                    daemon=True
                )
                self.receive_thread.start()
            
            self.logger.info(f"连接成功: {target_id} ({host}:{port})")
            return True
            
        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            return False
    
    def disconnect(self, target_id: str):
        """断开连接"""
        self._close_connection(target_id)
    
    def _receive_loop(self, target_id: str, sock: socket.socket):
        """接收消息循环"""
        try:
            while target_id in self.connections:
                message = self._receive_message(sock)
                if not message:
                    break
                
                # 处理消息
                self._handle_message(message)
                
        except Exception as e:
            self.logger.error(f"接收消息循环异常: {e}")
        finally:
            self._close_connection(target_id)
