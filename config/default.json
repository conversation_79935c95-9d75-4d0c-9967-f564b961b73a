{"manager": {"host": "localhost", "port": 8080, "max_workers": 10, "heartbeat_timeout": 60, "job_cleanup_interval": 300}, "runner": {"heartbeat_interval": 30, "max_concurrent_jobs": 3, "resource_check_interval": 10, "default_limits": {"max_cpu_percent": 80.0, "max_memory_mb": 1024, "max_runtime_seconds": 3600}}, "job": {"default_timeout": 1800, "max_retries": 3, "retry_delay": 60}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/cluster.log", "max_size": "10MB", "backup_count": 5}, "database": {"type": "sqlite", "path": "data/cluster.db"}}