"""
集群任务管理平台 - Runner核心类
"""

import threading
import time
import requests
import json
from typing import Dict, Optional, List
from concurrent.futures import ThreadPoolExecutor, Future

from common import (
    get_logger, LoggerMixin, ConfigManager, Runner, Job, JobStatus, RunnerStatus,
    ResourceLimits, ResourceUsage, generate_id, get_timestamp, get_hostname, get_local_ip
)
from .resource_monitor import ResourceMonitor
from .job_executor import JobExecutor
from .communication import ManagerCommunicator


class ClusterRunner(LoggerMixin):
    """集群Runner主类"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.running = False
        self.shutdown_event = threading.Event()
        
        # Runner配置
        runner_config = config.get_section('runner')
        manager_config = config.get_section('manager')
        
        # 基本信息
        self.runner_id = runner_config.get('runner_id') or generate_id("runner")
        self.name = runner_config.get('name') or f"Runner-{get_hostname()}"
        self.host = get_local_ip()
        self.port = runner_config.get('port', 0)  # 0表示自动分配
        self.capabilities = runner_config.get('capabilities', [])
        
        # 管理程序连接信息
        self.manager_host = manager_config.get('host', 'localhost')
        self.manager_port = manager_config.get('port', 8080)
        self.manager_url = f"http://{self.manager_host}:{self.manager_port}"
        
        # 运行参数
        self.heartbeat_interval = runner_config.get('heartbeat_interval', 30)
        self.max_concurrent_jobs = runner_config.get('max_concurrent_jobs', 3)
        self.resource_check_interval = runner_config.get('resource_check_interval', 10)
        
        # 资源限制
        limits_config = runner_config.get('default_limits', {})
        self.resource_limits = ResourceLimits(
            max_cpu_percent=limits_config.get('max_cpu_percent', 80.0),
            max_memory_mb=limits_config.get('max_memory_mb', 1024),
            max_runtime_seconds=limits_config.get('max_runtime_seconds', 3600)
        )
        
        # 创建Runner对象
        self.runner_info = Runner(
            runner_id=self.runner_id,
            name=self.name,
            host=self.host,
            port=self.port,
            status=RunnerStatus.IDLE,
            resource_limits=self.resource_limits,
            capabilities=self.capabilities
        )
        
        # 初始化子组件
        self.resource_monitor = ResourceMonitor(config, self.resource_limits)
        self.job_executor = JobExecutor(config, self.max_concurrent_jobs)
        self.communicator = ManagerCommunicator(config, self.manager_url)
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_jobs + 2)
        
        # 后台任务线程
        self.background_threads = []
        
        # 运行状态
        self.start_time = None
        self.registered = False
        
        self.logger.info(f"Runner初始化完成: {self.runner_id}")
    
    def start(self):
        """启动Runner"""
        if self.running:
            self.logger.warning("Runner已经在运行")
            return
        
        self.logger.info("正在启动Runner...")
        
        try:
            self.start_time = get_timestamp()
            
            # 启动子组件
            self.resource_monitor.start()
            self.job_executor.start()
            self.communicator.start()
            
            # 注册到管理程序
            self._register_to_manager()
            
            # 启动后台任务
            self._start_background_tasks()
            
            self.running = True
            self.logger.info("Runner启动成功")
            
        except Exception as e:
            self.logger.error(f"启动Runner失败: {e}")
            self.shutdown()
            raise
    
    def _register_to_manager(self):
        """注册到管理程序"""
        try:
            success = self.communicator.register_runner(self.runner_info)
            if success:
                self.registered = True
                self.logger.info(f"成功注册到管理程序: {self.manager_url}")
            else:
                raise RuntimeError("注册失败")
        except Exception as e:
            self.logger.error(f"注册到管理程序失败: {e}")
            raise
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 心跳任务
        heartbeat_thread = threading.Thread(
            target=self._heartbeat_task,
            name="HeartbeatTask",
            daemon=True
        )
        heartbeat_thread.start()
        self.background_threads.append(heartbeat_thread)
        
        # 任务请求任务
        job_request_thread = threading.Thread(
            target=self._job_request_task,
            name="JobRequestTask",
            daemon=True
        )
        job_request_thread.start()
        self.background_threads.append(job_request_thread)
        
        # 资源检查任务
        resource_check_thread = threading.Thread(
            target=self._resource_check_task,
            name="ResourceCheckTask",
            daemon=True
        )
        resource_check_thread.start()
        self.background_threads.append(resource_check_thread)
        
        # 生命周期管理任务
        lifecycle_thread = threading.Thread(
            target=self._lifecycle_task,
            name="LifecycleTask",
            daemon=True
        )
        lifecycle_thread.start()
        self.background_threads.append(lifecycle_thread)
        
        self.logger.info(f"启动了 {len(self.background_threads)} 个后台任务")
    
    def _heartbeat_task(self):
        """心跳任务"""
        self.logger.info("心跳任务启动")
        
        while not self.shutdown_event.is_set():
            try:
                if self.registered:
                    # 获取当前状态
                    resource_usage = self.resource_monitor.get_current_usage()
                    current_job_id = self.job_executor.get_current_job_id()
                    
                    # 确定Runner状态
                    if self.job_executor.is_busy():
                        status = RunnerStatus.BUSY
                    elif self._should_stop():
                        status = RunnerStatus.STOPPING
                    else:
                        status = RunnerStatus.IDLE
                    
                    # 发送心跳
                    self.communicator.send_heartbeat(
                        self.runner_id, status, resource_usage, current_job_id
                    )
                
                # 等待下一次心跳
                self.shutdown_event.wait(self.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"心跳任务异常: {e}")
                self.shutdown_event.wait(10)
    
    def _job_request_task(self):
        """任务请求任务"""
        self.logger.info("任务请求任务启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 检查是否可以接收新任务
                if (self.registered and 
                    not self._should_stop() and 
                    self.job_executor.can_accept_job() and
                    self.resource_monitor.has_available_resources()):
                    
                    # 请求新任务
                    job = self.communicator.request_job(self.runner_id, self.capabilities)
                    
                    if job:
                        # 执行任务
                        self._execute_job(job)
                
                # 等待下一次检查
                self.shutdown_event.wait(5)
                
            except Exception as e:
                self.logger.error(f"任务请求任务异常: {e}")
                self.shutdown_event.wait(10)
    
    def _resource_check_task(self):
        """资源检查任务"""
        self.logger.info("资源检查任务启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 更新资源使用情况
                self.resource_monitor.update_usage()
                
                # 检查资源限制
                if self.resource_monitor.is_resource_exhausted():
                    self.logger.warning("资源耗尽，停止接收新任务")
                    # 这里可以设置标志位，阻止接收新任务
                
                # 等待下一次检查
                self.shutdown_event.wait(self.resource_check_interval)
                
            except Exception as e:
                self.logger.error(f"资源检查任务异常: {e}")
                self.shutdown_event.wait(10)
    
    def _lifecycle_task(self):
        """生命周期管理任务"""
        self.logger.info("生命周期管理任务启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 检查是否应该停止
                if self._should_stop():
                    self.logger.info("达到生命周期限制，准备停止Runner")
                    
                    # 等待当前任务完成
                    self.job_executor.wait_for_completion()
                    
                    # 主动关闭
                    self.shutdown()
                    break
                
                # 每分钟检查一次
                self.shutdown_event.wait(60)
                
            except Exception as e:
                self.logger.error(f"生命周期管理任务异常: {e}")
                self.shutdown_event.wait(10)
    
    def _should_stop(self) -> bool:
        """检查是否应该停止"""
        if not self.start_time:
            return False
        
        # 检查运行时间限制
        runtime = get_timestamp() - self.start_time
        if runtime >= self.resource_limits.max_runtime_seconds:
            return True
        
        # 检查资源使用情况
        if self.resource_monitor.is_resource_exhausted():
            return True
        
        return False
    
    def _execute_job(self, job: Job):
        """执行任务"""
        try:
            self.logger.info(f"开始执行任务: {job.job_id} - {job.name}")
            
            # 提交任务给执行器
            future = self.job_executor.submit_job(job)
            
            # 在后台处理任务完成
            def handle_completion(fut: Future):
                try:
                    result = fut.result()
                    success = result.get('success', False)
                    output = result.get('output', {})
                    error = result.get('error')
                    
                    # 报告任务状态
                    if success:
                        self.communicator.report_job_status(
                            job.job_id, JobStatus.COMPLETED, self.runner_id, output
                        )
                        self.logger.info(f"任务完成: {job.job_id}")
                    else:
                        self.communicator.report_job_status(
                            job.job_id, JobStatus.FAILED, self.runner_id, 
                            error_message=error
                        )
                        self.logger.error(f"任务失败: {job.job_id} - {error}")
                        
                except Exception as e:
                    self.logger.error(f"处理任务完成异常: {e}")
                    self.communicator.report_job_status(
                        job.job_id, JobStatus.FAILED, self.runner_id,
                        error_message=str(e)
                    )
            
            future.add_done_callback(handle_completion)
            
        except Exception as e:
            self.logger.error(f"执行任务失败: {e}")
            self.communicator.report_job_status(
                job.job_id, JobStatus.FAILED, self.runner_id,
                error_message=str(e)
            )
    
    def run(self):
        """运行Runner（阻塞）"""
        if not self.running:
            raise RuntimeError("Runner未启动")
        
        self.logger.info("Runner开始运行...")
        
        try:
            # 等待关闭信号
            self.shutdown_event.wait()
        except KeyboardInterrupt:
            self.logger.info("接收到中断信号")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """关闭Runner"""
        if not self.running:
            return
        
        self.logger.info("正在关闭Runner...")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 注销Runner
        if self.registered:
            try:
                self.communicator.unregister_runner(self.runner_id)
                self.registered = False
            except Exception as e:
                self.logger.error(f"注销Runner失败: {e}")
        
        # 关闭子组件
        self.job_executor.shutdown()
        self.resource_monitor.shutdown()
        self.communicator.shutdown()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 等待后台线程结束
        for thread in self.background_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.running = False
        self.logger.info("Runner已关闭")
    
    def get_status(self) -> Dict:
        """获取Runner状态"""
        return {
            "runner_id": self.runner_id,
            "name": self.name,
            "running": self.running,
            "registered": self.registered,
            "resource_usage": self.resource_monitor.get_current_usage(),
            "job_executor": self.job_executor.get_status(),
            "uptime": time.time() - (self.start_time or time.time())
        }
