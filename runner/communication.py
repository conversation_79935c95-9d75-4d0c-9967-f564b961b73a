"""
集群任务管理平台 - Runner通信模块
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any

from common import (
    get_logger, LoggerMixin, Config<PERSON>anager, Runner, Job, JobStatus, RunnerStatus,
    MessageSerializer, retry_on_exception, get_timestamp
)


class ManagerCommunicator(LoggerMixin):
    """管理程序通信器"""
    
    def __init__(self, config: ConfigManager, manager_url: str):
        self.config = config
        self.manager_url = manager_url.rstrip('/')
        self.running = False
        
        # 通信配置
        comm_config = config.get('communication', {})
        self.timeout = comm_config.get('timeout', 30)
        self.max_retries = comm_config.get('max_retries', 3)
        self.retry_delay = comm_config.get('retry_delay', 5)
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'ClusterRunner/1.0'
        })
        
        self.logger.info(f"通信器初始化完成，管理程序地址: {manager_url}")
    
    def start(self):
        """启动通信器"""
        if self.running:
            return
        
        self.logger.info("启动通信器...")
        
        # 测试连接
        if not self._test_connection():
            raise RuntimeError("无法连接到管理程序")
        
        self.running = True
        self.logger.info("通信器启动成功")
    
    def shutdown(self):
        """关闭通信器"""
        if not self.running:
            return
        
        self.logger.info("关闭通信器...")
        
        # 关闭HTTP会话
        self.session.close()
        
        self.running = False
        self.logger.info("通信器已关闭")
    
    def _test_connection(self) -> bool:
        """测试与管理程序的连接"""
        try:
            response = self._make_request('GET', '/api/v1/status')
            return response is not None
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
    
    @retry_on_exception(max_retries=3, delay=1.0)
    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Optional[Dict]:
        """发送HTTP请求"""
        url = f"{self.manager_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(
                    url, 
                    data=json.dumps(data) if data else None,
                    timeout=self.timeout
                )
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, timeout=self.timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 检查响应状态
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                self.logger.warning(f"资源不存在: {endpoint}")
                return None
            else:
                self.logger.error(f"HTTP请求失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.exceptions.Timeout:
            self.logger.error(f"请求超时: {url}")
            raise
        except requests.exceptions.ConnectionError:
            self.logger.error(f"连接错误: {url}")
            raise
        except Exception as e:
            self.logger.error(f"请求异常: {url} - {e}")
            raise
        
        return None
    
    def register_runner(self, runner: Runner) -> bool:
        """注册Runner到管理程序"""
        try:
            self.logger.info(f"注册Runner: {runner.runner_id}")
            
            # 序列化Runner信息
            runner_data = json.loads(MessageSerializer.serialize_runner(runner))
            
            # 发送注册请求
            response = self._make_request('POST', '/api/v1/runners/register', runner_data)
            
            if response and response.get('success'):
                self.logger.info(f"Runner注册成功: {runner.runner_id}")
                return True
            else:
                self.logger.error(f"Runner注册失败: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"注册Runner失败: {e}")
            return False
    
    def unregister_runner(self, runner_id: str) -> bool:
        """从管理程序注销Runner"""
        try:
            self.logger.info(f"注销Runner: {runner_id}")
            
            # 发送注销请求
            response = self._make_request('POST', f'/api/v1/runners/{runner_id}/unregister')
            
            if response and response.get('success'):
                self.logger.info(f"Runner注销成功: {runner_id}")
                return True
            else:
                self.logger.error(f"Runner注销失败: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"注销Runner失败: {e}")
            return False
    
    def send_heartbeat(self, runner_id: str, status: RunnerStatus, 
                      resource_usage: Dict, current_job_id: Optional[str] = None) -> bool:
        """发送心跳"""
        try:
            heartbeat_data = {
                'status': status.value,
                'resource_usage': resource_usage,
                'current_job_id': current_job_id,
                'timestamp': get_timestamp()
            }
            
            # 发送心跳请求
            response = self._make_request('POST', f'/api/v1/runners/{runner_id}/heartbeat', heartbeat_data)
            
            if response and response.get('success'):
                self.logger.debug(f"心跳发送成功: {runner_id}")
                return True
            else:
                self.logger.warning(f"心跳发送失败: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送心跳失败: {e}")
            return False
    
    def request_job(self, runner_id: str, capabilities: List[str] = None) -> Optional[Job]:
        """请求新任务"""
        try:
            # 构建查询参数
            params = {'runner_id': runner_id}
            if capabilities:
                params['capabilities'] = capabilities
            
            # 构建URL
            endpoint = '/api/v1/jobs/next'
            if params:
                query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                endpoint += f"?{query_string}"
            
            # 发送请求
            response = self._make_request('GET', endpoint)
            
            if response and response.get('success'):
                job_data = response.get('data')
                if job_data and 'job_id' in job_data:
                    # 反序列化任务
                    job = MessageSerializer.deserialize_job(json.dumps(job_data))
                    self.logger.info(f"获取到新任务: {job.job_id}")
                    return job
                else:
                    self.logger.debug("没有可用任务")
                    return None
            else:
                self.logger.warning(f"请求任务失败: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"请求任务失败: {e}")
            return None
    
    def report_job_status(self, job_id: str, status: JobStatus, runner_id: str,
                         result: Dict = None, error_message: str = None) -> bool:
        """报告任务状态"""
        try:
            status_data = {
                'status': status.value,
                'runner_id': runner_id,
                'timestamp': get_timestamp()
            }
            
            if result:
                status_data['result'] = result
            if error_message:
                status_data['error_message'] = error_message
            
            # 发送状态更新请求
            response = self._make_request('POST', f'/api/v1/jobs/{job_id}/status', status_data)
            
            if response and response.get('success'):
                self.logger.info(f"任务状态报告成功: {job_id} - {status.value}")
                return True
            else:
                self.logger.error(f"任务状态报告失败: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"报告任务状态失败: {e}")
            return False
    
    def get_manager_status(self) -> Optional[Dict]:
        """获取管理程序状态"""
        try:
            response = self._make_request('GET', '/api/v1/status')
            
            if response and response.get('success'):
                return response.get('data')
            else:
                self.logger.warning(f"获取管理程序状态失败: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取管理程序状态失败: {e}")
            return None
    
    def submit_job(self, job: Job) -> Optional[str]:
        """提交任务到管理程序"""
        try:
            # 序列化任务
            job_data = json.loads(MessageSerializer.serialize_job(job))
            
            # 发送提交请求
            response = self._make_request('POST', '/api/v1/jobs', job_data)
            
            if response and response.get('success'):
                job_id = response.get('data', {}).get('job_id')
                self.logger.info(f"任务提交成功: {job_id}")
                return job_id
            else:
                self.logger.error(f"任务提交失败: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"提交任务失败: {e}")
            return None
    
    def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        try:
            response = self._make_request('DELETE', f'/api/v1/jobs/{job_id}')
            
            if response and response.get('success'):
                self.logger.info(f"任务取消成功: {job_id}")
                return True
            else:
                self.logger.error(f"任务取消失败: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False
    
    def get_job_info(self, job_id: str) -> Optional[Dict]:
        """获取任务信息"""
        try:
            response = self._make_request('GET', f'/api/v1/jobs/{job_id}')
            
            if response and response.get('success'):
                return response.get('data')
            else:
                self.logger.warning(f"获取任务信息失败: {response}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取任务信息失败: {e}")
            return None
    
    def ping(self) -> bool:
        """检查与管理程序的连接"""
        try:
            response = self._make_request('GET', '/api/v1/status')
            return response is not None and response.get('success', False)
        except Exception:
            return False
    
    def get_status(self) -> Dict:
        """获取通信器状态"""
        return {
            'running': self.running,
            'manager_url': self.manager_url,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'connected': self.ping() if self.running else False
        }
