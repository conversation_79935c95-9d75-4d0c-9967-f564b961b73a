"""
集群任务管理平台 - Runner任务执行器
"""

import subprocess
import threading
import time
import os
import signal
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, Empty

from common import (
    get_logger, LoggerMixin, ConfigManager, Job, JobStatus,
    get_timestamp, timeout_handler
)


class JobExecutor(LoggerMixin):
    """任务执行器"""
    
    def __init__(self, config: ConfigManager, max_concurrent_jobs: int = 3):
        self.config = config
        self.max_concurrent_jobs = max_concurrent_jobs
        self.running = False
        
        # 任务管理
        self.active_jobs: Dict[str, Dict] = {}  # job_id -> job_info
        self.job_queue = Queue()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_jobs)
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 配置参数
        job_config = config.get_section('job')
        self.default_timeout = job_config.get('default_timeout', 1800)
        self.shell_timeout = job_config.get('shell_timeout', 3600)
        
        self.logger.info(f"任务执行器初始化完成，最大并发数: {max_concurrent_jobs}")
    
    def start(self):
        """启动任务执行器"""
        if self.running:
            return
        
        self.logger.info("启动任务执行器...")
        self.running = True
        self.logger.info("任务执行器启动成功")
    
    def shutdown(self):
        """关闭任务执行器"""
        if not self.running:
            return
        
        self.logger.info("关闭任务执行器...")
        
        # 取消所有活动任务
        with self.lock:
            for job_id, job_info in self.active_jobs.items():
                self._cancel_job(job_id, job_info)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.running = False
        self.logger.info("任务执行器已关闭")
    
    def submit_job(self, job: Job) -> Future:
        """提交任务执行"""
        if not self.running:
            raise RuntimeError("任务执行器未启动")
        
        with self.lock:
            if len(self.active_jobs) >= self.max_concurrent_jobs:
                raise RuntimeError("已达到最大并发任务数限制")
            
            # 创建任务信息
            job_info = {
                'job': job,
                'start_time': get_timestamp(),
                'process': None,
                'future': None,
                'cancelled': False
            }
            
            # 提交任务到线程池
            future = self.executor.submit(self._execute_job, job, job_info)
            job_info['future'] = future
            
            # 记录活动任务
            self.active_jobs[job.job_id] = job_info
            
            self.logger.info(f"提交任务执行: {job.job_id} - {job.name}")
            
            # 添加完成回调
            future.add_done_callback(lambda f: self._on_job_completed(job.job_id))
            
            return future
    
    def _execute_job(self, job: Job, job_info: Dict) -> Dict[str, Any]:
        """执行任务"""
        try:
            self.logger.info(f"开始执行任务: {job.job_id}")
            
            # 准备执行环境
            env = os.environ.copy()
            env.update(job.env or {})
            
            # 设置工作目录
            cwd = job.working_dir or os.getcwd()
            if not os.path.exists(cwd):
                os.makedirs(cwd, exist_ok=True)
            
            # 构建命令
            cmd = [job.command] + (job.args or [])
            
            # 确定超时时间
            timeout = getattr(job, 'timeout', None) or self.default_timeout
            
            # 执行命令
            result = self._run_command(cmd, cwd, env, timeout, job_info)
            
            self.logger.info(f"任务执行完成: {job.job_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"任务执行异常: {job.job_id} - {e}")
            return {
                'success': False,
                'error': str(e),
                'output': {},
                'exit_code': -1
            }
    
    def _run_command(self, cmd: List[str], cwd: str, env: Dict[str, str], 
                    timeout: int, job_info: Dict) -> Dict[str, Any]:
        """运行命令"""
        try:
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 记录进程
            job_info['process'] = process
            
            try:
                # 等待进程完成
                stdout, stderr = process.communicate(timeout=timeout)
                exit_code = process.returncode
                
                # 检查是否被取消
                if job_info.get('cancelled', False):
                    return {
                        'success': False,
                        'error': 'Job was cancelled',
                        'output': {'stdout': stdout, 'stderr': stderr},
                        'exit_code': exit_code
                    }
                
                # 返回结果
                success = exit_code == 0
                return {
                    'success': success,
                    'error': stderr if not success else None,
                    'output': {
                        'stdout': stdout,
                        'stderr': stderr,
                        'exit_code': exit_code
                    },
                    'exit_code': exit_code
                }
                
            except subprocess.TimeoutExpired:
                # 超时处理
                self.logger.warning(f"任务执行超时: {timeout}秒")
                process.kill()
                stdout, stderr = process.communicate()
                
                return {
                    'success': False,
                    'error': f'Job execution timeout after {timeout} seconds',
                    'output': {'stdout': stdout, 'stderr': stderr},
                    'exit_code': -1
                }
                
        except Exception as e:
            self.logger.error(f"运行命令失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'output': {},
                'exit_code': -1
            }
    
    def _cancel_job(self, job_id: str, job_info: Dict):
        """取消任务"""
        try:
            job_info['cancelled'] = True
            
            # 终止进程
            process = job_info.get('process')
            if process and process.poll() is None:
                try:
                    # 先尝试优雅终止
                    process.terminate()
                    
                    # 等待一段时间
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 强制终止
                        process.kill()
                        process.wait()
                    
                    self.logger.info(f"已终止任务进程: {job_id}")
                    
                except Exception as e:
                    self.logger.error(f"终止任务进程失败: {job_id} - {e}")
            
            # 取消Future
            future = job_info.get('future')
            if future and not future.done():
                future.cancel()
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {job_id} - {e}")
    
    def _on_job_completed(self, job_id: str):
        """任务完成回调"""
        with self.lock:
            if job_id in self.active_jobs:
                job_info = self.active_jobs.pop(job_id)
                duration = get_timestamp() - job_info['start_time']
                self.logger.info(f"任务完成: {job_id}，耗时: {duration:.2f}秒")
    
    def cancel_job(self, job_id: str) -> bool:
        """取消指定任务"""
        with self.lock:
            if job_id not in self.active_jobs:
                return False
            
            job_info = self.active_jobs[job_id]
            self._cancel_job(job_id, job_info)
            
            self.logger.info(f"已取消任务: {job_id}")
            return True
    
    def get_active_jobs(self) -> List[str]:
        """获取活动任务列表"""
        with self.lock:
            return list(self.active_jobs.keys())
    
    def get_current_job_id(self) -> Optional[str]:
        """获取当前执行的任务ID（如果只有一个）"""
        with self.lock:
            active_jobs = list(self.active_jobs.keys())
            return active_jobs[0] if len(active_jobs) == 1 else None
    
    def is_busy(self) -> bool:
        """检查是否正在执行任务"""
        with self.lock:
            return len(self.active_jobs) > 0
    
    def can_accept_job(self) -> bool:
        """检查是否可以接受新任务"""
        with self.lock:
            return len(self.active_jobs) < self.max_concurrent_jobs
    
    def get_job_info(self, job_id: str) -> Optional[Dict]:
        """获取任务信息"""
        with self.lock:
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                return None
            
            job = job_info['job']
            start_time = job_info['start_time']
            current_time = get_timestamp()
            
            return {
                'job_id': job.job_id,
                'name': job.name,
                'command': job.command,
                'args': job.args,
                'start_time': start_time,
                'duration': current_time - start_time,
                'status': 'running'
            }
    
    def wait_for_completion(self, timeout: Optional[int] = None):
        """等待所有任务完成"""
        start_time = time.time()
        
        while True:
            with self.lock:
                if not self.active_jobs:
                    break
            
            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                self.logger.warning("等待任务完成超时")
                break
            
            time.sleep(1)
        
        self.logger.info("所有任务已完成")
    
    def get_status(self) -> Dict:
        """获取执行器状态"""
        with self.lock:
            active_job_info = []
            for job_id, job_info in self.active_jobs.items():
                info = self.get_job_info(job_id)
                if info:
                    active_job_info.append(info)
            
            return {
                'running': self.running,
                'max_concurrent_jobs': self.max_concurrent_jobs,
                'active_jobs_count': len(self.active_jobs),
                'active_jobs': active_job_info,
                'can_accept_job': self.can_accept_job(),
                'is_busy': self.is_busy()
            }
