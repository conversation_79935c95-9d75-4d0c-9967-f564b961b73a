"""
集群任务管理平台 - Runner资源监控器
"""

import threading
import time
import psutil
import os
from typing import Dict, Optional

from common import (
    get_logger, LoggerMixin, ConfigManager, ResourceLimits, ResourceUsage,
    get_timestamp
)


class ResourceMonitor(LoggerMixin):
    """资源监控器"""
    
    def __init__(self, config: Config<PERSON>anager, resource_limits: ResourceLimits):
        self.config = config
        self.resource_limits = resource_limits
        self.running = False
        
        # 当前资源使用情况
        self.current_usage = ResourceUsage()
        
        # 监控配置
        runner_config = config.get_section('runner')
        self.check_interval = runner_config.get('resource_check_interval', 10)
        
        # 进程信息
        self.process = psutil.Process()
        self.start_time = time.time()
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 监控线程
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        self.logger.info("资源监控器初始化完成")
    
    def start(self):
        """启动资源监控器"""
        if self.running:
            return
        
        self.logger.info("启动资源监控器...")
        
        self.running = True
        self.shutdown_event.clear()
        self.start_time = time.time()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="ResourceMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("资源监控器启动成功")
    
    def shutdown(self):
        """关闭资源监控器"""
        if not self.running:
            return
        
        self.logger.info("关闭资源监控器...")
        
        self.running = False
        self.shutdown_event.set()
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("资源监控器已关闭")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("资源监控循环启动")
        
        while not self.shutdown_event.is_set():
            try:
                # 更新资源使用情况
                self.update_usage()
                
                # 检查资源限制
                self._check_resource_limits()
                
                # 等待下一次检查
                self.shutdown_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"资源监控循环异常: {e}")
                self.shutdown_event.wait(5)
    
    def update_usage(self):
        """更新资源使用情况"""
        try:
            with self.lock:
                # CPU使用率
                self.current_usage.cpu_percent = self.process.cpu_percent()
                
                # 内存使用量
                memory_info = self.process.memory_info()
                self.current_usage.memory_mb = memory_info.rss // (1024 * 1024)
                
                # 运行时间
                self.current_usage.runtime_seconds = int(time.time() - self.start_time)
                
                # 更新时间戳
                self.current_usage.timestamp = get_timestamp()
                
                self.logger.debug(
                    f"资源使用情况: CPU {self.current_usage.cpu_percent:.1f}%, "
                    f"内存 {self.current_usage.memory_mb}MB, "
                    f"运行时间 {self.current_usage.runtime_seconds}s"
                )
                
        except Exception as e:
            self.logger.error(f"更新资源使用情况失败: {e}")
    
    def _check_resource_limits(self):
        """检查资源限制"""
        try:
            # 检查CPU使用率
            if self.current_usage.cpu_percent > self.resource_limits.max_cpu_percent:
                self.logger.warning(
                    f"CPU使用率超限: {self.current_usage.cpu_percent:.1f}% > "
                    f"{self.resource_limits.max_cpu_percent}%"
                )
            
            # 检查内存使用量
            if self.current_usage.memory_mb > self.resource_limits.max_memory_mb:
                self.logger.warning(
                    f"内存使用量超限: {self.current_usage.memory_mb}MB > "
                    f"{self.resource_limits.max_memory_mb}MB"
                )
            
            # 检查运行时间
            if self.current_usage.runtime_seconds > self.resource_limits.max_runtime_seconds:
                self.logger.warning(
                    f"运行时间超限: {self.current_usage.runtime_seconds}s > "
                    f"{self.resource_limits.max_runtime_seconds}s"
                )
                
        except Exception as e:
            self.logger.error(f"检查资源限制失败: {e}")
    
    def get_current_usage(self) -> Dict:
        """获取当前资源使用情况"""
        with self.lock:
            return {
                'cpu_percent': self.current_usage.cpu_percent,
                'memory_mb': self.current_usage.memory_mb,
                'runtime_seconds': self.current_usage.runtime_seconds,
                'timestamp': self.current_usage.timestamp
            }
    
    def has_available_resources(self) -> bool:
        """检查是否有可用资源"""
        with self.lock:
            # 检查CPU
            if self.current_usage.cpu_percent >= self.resource_limits.max_cpu_percent * 0.9:
                return False
            
            # 检查内存
            if self.current_usage.memory_mb >= self.resource_limits.max_memory_mb * 0.9:
                return False
            
            # 检查运行时间（留10%缓冲）
            if self.current_usage.runtime_seconds >= self.resource_limits.max_runtime_seconds * 0.9:
                return False
            
            return True
    
    def is_resource_exhausted(self) -> bool:
        """检查资源是否耗尽"""
        with self.lock:
            # 检查CPU
            if self.current_usage.cpu_percent >= self.resource_limits.max_cpu_percent:
                return True
            
            # 检查内存
            if self.current_usage.memory_mb >= self.resource_limits.max_memory_mb:
                return True
            
            # 检查运行时间
            if self.current_usage.runtime_seconds >= self.resource_limits.max_runtime_seconds:
                return True
            
            return False
    
    def get_resource_utilization(self) -> Dict[str, float]:
        """获取资源利用率"""
        with self.lock:
            return {
                'cpu_utilization': min(100.0, 
                    self.current_usage.cpu_percent / self.resource_limits.max_cpu_percent * 100),
                'memory_utilization': min(100.0,
                    self.current_usage.memory_mb / self.resource_limits.max_memory_mb * 100),
                'runtime_utilization': min(100.0,
                    self.current_usage.runtime_seconds / self.resource_limits.max_runtime_seconds * 100)
            }
    
    def get_remaining_resources(self) -> Dict[str, float]:
        """获取剩余资源"""
        with self.lock:
            return {
                'cpu_remaining_percent': max(0, 
                    self.resource_limits.max_cpu_percent - self.current_usage.cpu_percent),
                'memory_remaining_mb': max(0,
                    self.resource_limits.max_memory_mb - self.current_usage.memory_mb),
                'runtime_remaining_seconds': max(0,
                    self.resource_limits.max_runtime_seconds - self.current_usage.runtime_seconds)
            }
    
    def estimate_job_capacity(self, estimated_cpu: float = 10.0, 
                            estimated_memory: int = 100, 
                            estimated_duration: int = 300) -> bool:
        """估算是否能够执行指定资源需求的任务"""
        with self.lock:
            remaining = self.get_remaining_resources()
            
            # 检查CPU容量
            if estimated_cpu > remaining['cpu_remaining_percent']:
                return False
            
            # 检查内存容量
            if estimated_memory > remaining['memory_remaining_mb']:
                return False
            
            # 检查时间容量
            if estimated_duration > remaining['runtime_remaining_seconds']:
                return False
            
            return True
    
    def get_system_info(self) -> Dict:
        """获取系统信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'memory_total_mb': psutil.virtual_memory().total // (1024 * 1024),
                'disk_usage': {
                    'total_gb': psutil.disk_usage('/').total // (1024 * 1024 * 1024),
                    'used_gb': psutil.disk_usage('/').used // (1024 * 1024 * 1024),
                    'free_gb': psutil.disk_usage('/').free // (1024 * 1024 * 1024)
                },
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0],
                'boot_time': psutil.boot_time()
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def get_process_info(self) -> Dict:
        """获取进程信息"""
        try:
            return {
                'pid': self.process.pid,
                'name': self.process.name(),
                'status': self.process.status(),
                'create_time': self.process.create_time(),
                'num_threads': self.process.num_threads(),
                'memory_info': self.process.memory_info()._asdict(),
                'cpu_times': self.process.cpu_times()._asdict(),
                'open_files': len(self.process.open_files()),
                'connections': len(self.process.connections())
            }
        except Exception as e:
            self.logger.error(f"获取进程信息失败: {e}")
            return {}
    
    def get_status(self) -> Dict:
        """获取监控器状态"""
        with self.lock:
            return {
                'running': self.running,
                'check_interval': self.check_interval,
                'resource_limits': {
                    'max_cpu_percent': self.resource_limits.max_cpu_percent,
                    'max_memory_mb': self.resource_limits.max_memory_mb,
                    'max_runtime_seconds': self.resource_limits.max_runtime_seconds
                },
                'current_usage': self.get_current_usage(),
                'utilization': self.get_resource_utilization(),
                'remaining': self.get_remaining_resources(),
                'has_available_resources': self.has_available_resources(),
                'is_resource_exhausted': self.is_resource_exhausted()
            }
